#coding:utf-8
from flask import Flask, request, render_template,session, g, redirect, url_for, \
     abort,  flash, current_app, jsonify
import subprocess
import sqlite3
from flask import Flask, request, render_template
import json
import os, io, time,re
import platform
import threading
#from websocket import create_connection
from datetime import datetime,timedelta
from logparse import start
    
PROJECT = 'icm'
PROJECT = 'incloud'
ws_ip='************'
#ws_ip='************'
ws_port=18888
WS_PORT = 18888
k8s_ip='************'
#k8s_ip='************'
#k8s_ip='************'
k8s_port=8080
#k8s_port=18081
basePath = "/home/<USER>/incloudmanager"
basePath = "/home"
sysstr = platform.system()
# 假设日志时间格式为 'YYYY-MM-DD HH:MM:SS'，你可以根据日志的实际时间格式调整正则表达式
TIME_REGEX = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})')

def run_kubectl_cmd(cmd):
    popen = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = popen.communicate()
    if popen.returncode != 0:
        raise Exception(f"Command failed: {stderr.decode('utf-8')}")
    return stdout.decode('utf-8')

def index(req):
    req_cs = dict(req)
    data_list = []
    PROJECT = req.get('namespaces','incloud')
    WORK_PATH = current_app.config['WORK_PATH']
    req_cs = dict(req)    
    if req.get('name') != None and req.get('name').strip() != "":
        cmd = "kubectl get pod -n %s -o wide | grep %s"%(PROJECT,req.get('name'))
    else:
        cmd = "kubectl get pod -n %s -o wide"%(PROJECT)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    rows = popen.stdout.readlines()
    for row in rows:
        print (row)
        if b"RESTARTS" in row:
            continue
        data_list.append(row.decode('utf-8').split())
    cmd = "du -s %s/logs/*"%(WORK_PATH)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    rows = popen.stdout.readlines()
    data = {}
    for row in rows:
        line = row.decode('utf-8')
        if "No such file" in line:
            continue
        size,name = line.split()
        data[name.replace("%s/logs/"%(WORK_PATH),"")] = "{:.2f}".format(int(size)/1024.0)
    
    hours_times = list(data.keys())
#    return render_template("hadmin/index-pods.html",data_list=data_list,count=len(data_list),name = req.get('name'),project=PROJECT)
    return render_template("taillog/index.html",data_list=data_list,count=len(data_list),name = req.get('name'),namespaces=PROJECT,data=data,hours_times=hours_times)

def gocontainer(request):
    req = request.args
    k8s_ip , port = request.host.split(":")
    req_cs = dict(req)
    data_list = []
    pod = req.get('pod')
    name = pod.rsplit('-',2)[0]
    baseUrl = "ws://%s:%s"%(k8s_ip,k8s_port)
    selfLink = "/api/v1/namespaces/%s/pods/%s"%(PROJECT,pod)
    containerName = name
    return render_template("container-terminal/index.html",data_list=data_list,pod=pod,baseUrl=baseUrl,selfLink = selfLink,containerName=containerName)

def restart(req):
    req_cs = dict(req)
    data_list = []
    cmd = "kubectl delete pod -n %s %s"%(PROJECT,req.get('pod'))
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    s="valid"
    t={}
    t['success']=s
    return json.dumps(t,ensure_ascii=False)
    return redirect("/?name=%s"%req['pod'].split('-')[0])

def doRebuild(name):
    ws_server = "ws://127.0.0.1:%s/websocket/?pod=rebuild-%s"%(WS_PORT,name)
    for path, dirs, files in os.walk(basePath):
#        print path
        if len(path.split("/")) > 6:
            continue
        if path.split("/")[-1] == name and len(path.split("/")) <= 6:
            codePath = path
            break
    if codePath is None:
        ws.send("code path error")   #�����ݷ��͵�websocket�����
        return
    cmd = "cd %(codePath)s && mvn deploy -DskipTests && mvn docker:build  -DdockerImageTags=latest -DskipTests && docker tag harbor.inspur.com/incloudos-docker/%(name)s-service ************:5000/incloudos-docker/%(name)s-service && docker login -u admin -p 123456a? ************:5000 && docker push ************:5000/incloudos-docker/%(name)s-service && kubectl set image deployment/%(name)s %(name)s=************:5000/incloudos-docker/%(name)s-service -n %(project)s && kubectl get pod -n  %(project)s|grep  %(name)s|awk '{print $1}'|xargs kubectl delete pod -n  %(project)s "%{"name":name,"codePath":codePath,'project':PROJECT}
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
#    ws = create_connection(ws_server)   # ����websocket����
#    start = datetime.now()
#    while True:
#        line = popen.stdout.readline()  #��ȡ����
#        if line:
#            start = datetime.now()
#            ws.send(line.replace('\t',"&nbsp;&nbsp;&nbsp;&nbsp;").replace(' ',"&nbsp;"))   #�����ݷ��͵�websocket�����
#        if datetime.now()-start > timedelta(seconds=20):
#            break
#        print (time.time())

def rebuild(req):
    req_cs = dict(req)
    data_list = []
    name = req.get('pod').rsplit('-',2)[0]
    my_thread = threading.Thread(target=doRebuild, args=(name,))
    my_thread.start()
    s="valid"
    t={}
    t['success']=s
    return json.dumps(t,ensure_ascii=False)
    return redirect("/monitor?pod=rebuild-%s"%name)

def doMonitor(pod):
    ws_server = "ws://127.0.0.1:%s/websocket/?pod=%s"%(WS_PORT,pod)
    cmd = "kubectl logs -f --tail=500 -n %s %s"%(PROJECT,pod)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
#    ws = create_connection(ws_server)   # ����websocket����
#    start = datetime.now()
#    while True:
#        line = popen.stdout.readline()  #��ȡ����
#        if line:
#            start = datetime.now()
#            ws.send(line.replace('\t',"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;").replace(' ',"&nbsp;"))   #�����ݷ��͵�websocket�����
#        if datetime.now()-start > timedelta(seconds=200):
#            break        
#        print (time.time())

def monitor(request):
    ws_ip , ws_port = request.host.split(":")
    req = request.args
    req_cs = dict(req)
    pod = req.get('pod')
    ws_port = current_app.config['WS_PORT']
    if "rebuild-" in pod:
        return render_template("taillog/monitor.html",pod=pod,ws_ip=ws_ip,ws_port=ws_port)
    my_thread = threading.Thread(target=doMonitor, args=(pod,))
    my_thread.start()
    return render_template("taillog/monitor.html",pod=pod,ws_ip=ws_ip,ws_port=ws_port)


def getlog(req):
    req_cs = dict(req)
    data_list = []
    flag = req.get('flag',"")
    loglevel = req.get('loglevel',"")
    num = req.get('num',"1")
    start = req.get('start',"0")
    end = req.get('end',"0")
    pageSize = req.get('pageSize','300')
    keyworld = req.get('keyworld',"")
    search_name = req.get('search_name',"")
    start_time = req.get('start_time',"")
    end_time = req.get('end_time',"")
    pod = req.get('pod','')
    cmd = "kubectl logs -n %s %s"%(PROJECT,req.get('pod'))
    print(num,start,end,pageSize)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    data_list = popen.stdout.readlines()
    count = len(data_list)
    start = int(end)
#    end = count if int(end) + int(pageSize) > count else int(end) + int(pageSize)
    data_list_1 = []
    end = int(end)
    data = {"start":start,"end":end, "num":num}
    log_html = ""
    start_flag = False
    if start_time != "":
        start_flag = True
    for line in data_list[start:]:
        if len(data_list_1) >= int(pageSize):
            break
        end += 1
        line_tmp = line
        line = line.decode('utf-8')
        if loglevel != "" and loglevel == "ERROR" and "INFO" in line:
            continue
        if loglevel != "" and loglevel == "INFO" and not"INFO" in line:
            continue
        if search_name != "" and search_name not in line:
            continue
        if start_time != "" and start_flag:
            # 转换为 datetime 对象
            start_time_1 = datetime.strptime(start_time, '%Y-%m-%dT%H:%M')
            if "-" in line.split()[0] and ':' in line.split()[1]:
                log_time = datetime.strptime("%s %s"%(line.split()[0],line.split()[1].split(",")[0]), '%Y-%m-%d %H:%M:%S')
                if log_time  < start_time_1:
                    continue
                else:
                    start_flag = False
        if start_flag:
            continue
        if end_time != "":
            # 转换为 datetime 对象
            end_time_1 = datetime.strptime(end_time, '%Y-%m-%dT%H:%M')
            if "-" in line.split()[0] and ':' in line.split()[1]:
                log_time = datetime.strptime("%s %s"%(line.split()[0],line.split()[1]), '%Y-%m-%d %H:%M:%S,%f')
                if end_time_1 < log_time:
                    break
        if flag in("up","down"):
            line = line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;')
            if "ERROR" in line:
                log_html += f"<font color='#ff0000'>{line}</font></br>"
            elif "WARN" in line:
                log_html += f"<font color='#0000ff'>{line}</font></br>"
            else:
                log_html += f"<font color='#000000'>{line}</font></br>"
        data_list_1.append(line_tmp)
    print(num,start,end,pageSize)
    if flag in("up","down"):
        data["end"] = end
        print(data)
        data["html"] = log_html
        return jsonify(data)
    return render_template("taillog/getlogall.html",data_list=data_list_1,loglevel=loglevel,search_name=search_name,start_time=start_time,end_time=end_time,count=count,name = req.get('name'),pod=pod,keyworld=keyworld,num=num,start=start,end=pageSize)
    
def collectlog(req):
    WORK_PATH = current_app.config['WORK_PATH']
    req_cs = dict(req)
    data_list = []
    PROJECT = req.get('namespaces','incloud')
    if PROJECT == 'openstack':
        cmd = "kubectl cp -n %s %s:/var/lib/data/icos-log %s/logs/%s"%(PROJECT,req.get('pod'),WORK_PATH, req.get('pod'))
    elif "itask" in req.get('pod'):
        cmd = "kubectl cp -n %s %s:/deployments/incloudos/itask-service.log %s/logs/%s.log"%(PROJECT,req.get('pod'),WORK_PATH, req.get('pod'))
#    elif "icompute" in req.get('pod'):
#        cmd = "kubectl cp -n %s %s:/var/log/incloudos/cmp/logs %s/logs/%s"%(PROJECT,req.get('pod'),WORK_PATH, req.get('pod'))
    else:
        cmd = "kubectl cp -n %s %s:/deployments/incloudos/logs %s/logs/%s"%(PROJECT,req.get('pod'),WORK_PATH, req.get('pod'))
    print(cmd)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    data_list = popen.stdout.readlines()
    for root,dirs,files in os.walk("%s/logs/%s"%(WORK_PATH,req.get('pod'))):
        for file in files:
            if file.endswith(".gz"):
                cmd = "gunzip -c %s > %s"%(os.path.join(root,file),os.path.join(root,file[:-3]))
                print(cmd)
                popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
                data_list = popen.stdout.readlines()
    return redirect("/pods?name=%s&namespaces=%s"%(req['pod'].split('-')[0],PROJECT))

def mon(req):
    WORK_PATH = current_app.config['WORK_PATH']
    req_cs = dict(req)
    data_list = []
    logfile = "%s/logs/%s/invoke-time.log"%(WORK_PATH,req.get('pod'))
    if 'api-gateway' in req.get('pod'):
        logfile = "%s/logs/%s/apigateway.log"%(WORK_PATH,req.get('pod'))
    method = req.get('method',"")
    module = req.get('module',"")
    url = req.get('url',"")

    if os.path.exists(logfile):
        with open(logfile, 'r') as fp:
            mon_list = fp.readlines()
            if 'api-gateway' in req.get('pod'):
                for line in mon_list:
                    if not " ms " in line or not 'request:' in line:
                        continue
                    if url != '' and url not in line:
                        continue
                    if method != '' and method not in line:
                        continue
                    if module != '' and module not in line:
                        continue
                    lines = line.split()
                    data_list.append([lines[0],lines[1],lines[8].replace('<0><','').replace('>','').replace('[','').replace(']','')," ".join(lines[9:])])
            else:
                for line in mon_list:
                    if url != '' and url not in line:
                        continue
                    lines = line.split()
                    del lines[3]
                    del lines[2]
                    data_list.append([lines[0],lines[1],lines[2].replace('<0><','').replace('>','').replace('[','').replace(']','')," ".join(lines[3:])])
        return render_template("taillog/mon.html",data_list=data_list,pod=req.get('pod'),method=method ,module=module, url=url)
    else:
        return render_template("taillog/mon.html",data_list=[["请先进行日志收集操作"],[],[],[]])
def detaillog(req):
    req_cs = dict(req)
    data_list = []
    WORK_PATH = current_app.config['WORK_PATH']
    logfile = "%s/logs/%s/%s-service.log"%(WORK_PATH,req.get('pod'),req.get('pod').split("-")[0])
    if 'api-gateway' in req.get('pod'):
        logfile = "%s/logs/%s/apigateway.log"%(WORK_PATH,req.get('pod'))
    print (logfile)
    if os.path.exists(logfile):
        with open(logfile, 'r') as fp:
            mon_list = fp.readlines()
            requestid=req.get('requestid')
            for line in mon_list:
                if requestid in line:
                    data_list.append(line)
        return render_template("taillog/getlog.html",data_list=data_list)
    else:
        return render_template("taillog/mon.html",data_list=["请先进行日志收集操作！"])

def gettimelist(req):
    req_cs = dict(req)
    data_list = []
    requestid=req.get('requestid')
    WORK_PATH = current_app.config['WORK_PATH']
    cmd = "grep -n -r '%s' %s/logs/*" % (requestid, WORK_PATH)
    print(cmd)
    popen = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    raw_data_list = popen.stdout.readlines()

    # 解析日志行，并提取时间戳
    data = {}    
    parsed_data = []
    for line in raw_data_list:
        decoded_line = line.decode('utf-8')
        print(decoded_line)
        if "毫秒" in decoded_line:
            mod = decoded_line.replace(WORK_PATH,"").split("/")[2]
            t = decoded_line.split("毫秒")[0].split(":")[-1]
            data[mod] = t
        elif " ms" in decoded_line:
            mod = decoded_line.replace(WORK_PATH,"").split("/")[2]
            t = decoded_line.split(" ms")[0].split(" ")[-1]
            data[mod] = t
    print(data)
    hours_times = list(data.keys())
#    hours_times = ["gateway","istorage","inetwork"]
#    data = {"gateway":"100","istorage":"80","inetwork":"60"}
    return render_template("taillog/timereport.html",hours_times=hours_times,data=data)

def getdetaillog(req):
    req_cs = dict(req)
    logfile = req.get('file',"")
    flag = req.get('flag',"")
    num = req.get('num',"1")
    start = req.get('start',"0")
    end = req.get('end',"0")
    pageSize = req.get('pageSize','300')
    keyworld = req.get('keyworld',"")
    data_list = []
    print(num,start,end,pageSize)
    if os.path.exists(logfile):
        with open(logfile, 'r') as fp:
            mon_list = fp.readlines()
            count = len(mon_list)
            if flag in("up","down"):
                print(start,end)
                if flag == "up":
                    end = int(start)
                    start = 0 if int(start) - int(pageSize) < 0 else int(start) - int(pageSize)
                else:
                    start = int(end)
                    end = count if int(end) + int(pageSize) > count else int(end) + int(pageSize)
                data = {"start":start,"end":end, "num":num}
                log_html = ""
                for line in mon_list[start:end]:
                    line = line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;').replace(keyworld, '<span style="background-color: yellow;">' + keyworld + '</span>')
                    if "ERROR" in line:
                        log_html += f"<font color='#ff0000'>{line}</font></br>"
                    elif "WARN" in line:
                        log_html += f"<font color='#0000ff'>{line}</font></br>"
                    else:
                        log_html += f"<font color='#000000'>{line}</font></br>"
                data["html"] = log_html
                return jsonify(data) #json.dumps(data,ensure_ascii=False)
            else:
                start = 0 if int(num) - int(pageSize) < 0 else int(num) - int(pageSize)
                end = count if int(num) + int(pageSize) > count else int(num) + int(pageSize)
            data_list = mon_list[start:end]
        return render_template("taillog/searchresult.html",data_list=data_list,keyworld=keyworld,file=logfile,num=num,start=start,end=end)
    else:
        return render_template("taillog/searchresult.html",data_list=["请先进行日志收集操作！"],keyworld=keyworld)
def extract_time(log_line):
    """从日志行中提取时间"""
    match = TIME_REGEX.search(log_line)
    if match:
        try:
            # 将字符串解析为 datetime 对象
            return datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S,%f')
        except ValueError:
            return datetime.now()
    return datetime.now()

def search(req):
    req_cs = dict(req)
    keyworld = req.get('keyworld', "")
    PROJECT = req.get('namespaces', 'incloud')
    WORK_PATH = current_app.config['WORK_PATH']

    if keyworld == "":
        return render_template("taillog/search.html", data_list=["输入关键字:"], work_path=WORK_PATH, keyworld=keyworld, namespaces=PROJECT)

    data_list = []
    cmd = "grep -n -r '%s' %s/logs/*" % (keyworld, WORK_PATH)
    print(cmd)
    popen = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    raw_data_list = popen.stdout.readlines()

    # 解析日志行，并提取时间戳
    parsed_data = []
    for line in raw_data_list:
        decoded_line = line.decode('utf-8')
        log_time = extract_time(decoded_line)  # 提取时间
        if log_time:
            parsed_data.append((log_time, decoded_line))  # 使用 (时间戳, 日志行) 作为元组

    # 按时间戳进行排序
    sorted_data = sorted(parsed_data, key=lambda x: x[0], reverse=False)  # 降序排序，最新的在前

    # 只返回排序后的日志行
    sorted_log_lines = [item[1] for item in sorted_data]

    return render_template("taillog/search.html", data_list=sorted_log_lines, work_path=WORK_PATH, keyworld=keyworld, namespaces=PROJECT)
    
def execcmd(req):
    req_cs = dict(req)
    cmd = req.get('cmd',"")
    PROJECT = req.get('namespaces','incloud')
    WORK_PATH = current_app.config['WORK_PATH']
    if cmd == "":
        return render_template("taillog/search.html",data_list=["输入命令:"],cmd=cmd,keyworld="xxxxxxxxxxxxxxxxx",namespaces=PROJECT, work_path=WORK_PATH)
    data_list = []
    print(cmd)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    data_list = popen.stdout.readlines()
    # 解析日志行，并提取时间戳
    parsed_data = []
    for line in data_list:
        decoded_line = line.decode('utf-8')
        log_time = extract_time(decoded_line)  # 提取时间
        if log_time:
            parsed_data.append((log_time, decoded_line))  # 使用 (时间戳, 日志行) 作为元组

    # 按时间戳进行排序
    sorted_data = sorted(parsed_data, key=lambda x: x[0], reverse=False)  # 降序排序，最新的在前

    # 只返回排序后的日志行
    sorted_log_lines = [item[1] for item in sorted_data]
    
    return render_template("taillog/search.html",data_list=sorted_log_lines,keyworld="xxxxxxxxxxxxxxxxx",cmd=cmd,namespaces=PROJECT, work_path=WORK_PATH)

def report(req):
    req_cs = dict(req)
    cmd = req.get('cmd',"")
    PROJECT = req.get('namespaces','incloud')
    WORK_PATH = current_app.config['WORK_PATH']
    req_cs = dict(req)
    data_list = []
    logfile = "%s/logs/%s/invoke-time.log"%(WORK_PATH,req.get('pod'))
    if 'api-gateway' in req.get('pod'):
        logfile = "%s/logs/%s/apigateway.log"%(WORK_PATH,req.get('pod'))
    if not os.path.exists("%s/logs/%s"%(WORK_PATH,req.get('pod'))):
        return render_template("taillog/mon.html",data_list=[["请先进行日志收集操作！"],[],[],[]])
    if os.path.exists(logfile):
        data = start.parse_log_file(logfile)
        hours_times = sorted(list(data.get('hours_hits')))
        minutes_times = sorted(list(data.get('minutes_hits')))
        seconds_times = sorted(list(data.get('second_hits')))
#        print(minutes_times)
#        print(seconds_times)
        return render_template("taillog/report.html",data=data,
                                  web_log_urls_file=data.get('source_file',"") + '_urls.html',
                                  second_line_flag=True,
                                  hours_times=hours_times,
                                  minutes_times=minutes_times,
                                  seconds_times=seconds_times,
                                  method_counts=data.get('method_counts'),
                                  cost_time_range_percentile=data.get('cost_time_range_percentile'),
                                  cost_time_list=data.get('cost_time_list'),
                                  cost_time_flag=data.get('cost_time_flag'),
                                  cost_time_percentile_flag=data.get('cost_time_percentile_flag'),
                                  cost_time_threshold=data.get('cost_time_threshold'),
                                  cost_time_range=data.get('cost_time_range'),
                                  status_codes=data.get('status_codes'),
                                  status_codes_keys=data.get('status_codes').keys())
    return render_template("taillog/mon.html",data_list=[["此功能需要先开启tlog的enable-invoke-time-print！"],[],[],[]])
