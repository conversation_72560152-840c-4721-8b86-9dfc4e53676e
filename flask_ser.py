#coding:gbk
import sys
import os , time
#sys.path.append(os.path.split(os.path.realpath(__file__))[0])
#sys.path.append(os.path.realpath(__file__).replace("flask_ser.py","libs"))
from flask import Flask, request, render_template,session, g, redirect, url_for, \
     abort,  flash
from public import app, socketio
from utils.command import ps_command, top_command, tail_command

import json
import sqlite3
from contextlib import closing
#import MySQLdb as mysql
#
# configuration
#DATABASE = 'static/db/flaskr.db'
DEBUG = True
SECRET_KEY = 'development key'
USERNAME = '1'
PASSWORD = '1'
current_directory = os.getcwd()
#app = Flask(__name__)
app.config.from_object(__name__)
app.config.from_envvar('FLASKR_SETTINGS', silent=True)
app.config["WORK_PATH"] = current_directory

def close_firewalld():
    os.system("systemctl stop firewalld.service")
close_firewalld()

#def connect_db():
#    return sqlite3.connect(app.config['DATABASE'])
 
#def init_db():
#    with closing(connect_db()) as db:
#        with app.open_resource('static/db/deploy.sql') as f:
##        with app.open_resource('static/db/table.sql') as f:
##        with app.open_resource('static/db/data.sql') as f:
#            db.cursor().executescript(f.read())
#        db.commit()
#init_db()
#db = mysql.connect(user="wallmap", passwd="12345678",host="************", db="test", charset="utf8")
#db.autocommit(True)
#cur = db.cursor()
# 格式化时间
def now():
    """
    :return: str
    """
    return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))

import datetime
@app.before_request
def before_request():
    return
#    g.db = connect_db()
 
@app.after_request
def after_request(response):
    #####
#    cur1 = g.db.execute("select * from operate_log order by updatetime desc")
#    dict_list = [dict(id=str(row[0]),ip=str(row[1]),updatetime=str(row[4]),url=str(row[5])) for row in cur1.fetchall()]
#    print "after_request",dict_list
    #####
#    if not "/static/" in request.url:
#        g.db.execute("insert into operate_log(ip , method , status_code , url , param , referer , agent , updatetime) values(? , ? ,? ,? ,? ,? , ? , ? )",
#        [request.headers.get("X-Real-Ip" , request.remote_addr) , request.method , response.status_code , request.url , str(request.args) , request.headers.get("Referer") , request.headers.get("User-Agent") , now() ])
#        g.db.commit()
#    g.db.close()
    return response

import taillog
@app.route("/" , methods=['GET','POST'])
@app.route("/pods" , methods=['GET','POST'])
def pods():
    req = request.args
    return taillog.index( req )
@app.route("/restart" , methods=['GET','POST'])
def restart():
    req = request.args
    return taillog.restart( req )
@app.route("/rebuild" , methods=['GET','POST'])
def rebuild():
    req = request.args
    return taillog.rebuild( req )
@app.route("/monitor" , methods=['GET','POST'])
def monitor():
    return taillog.monitor( request )
@app.route("/getlog" , methods=['GET','POST'])
def getlog():
    req = request.args
    return taillog.getlog( req )
@app.route("/gocontainer" , methods=['GET','POST'])
def gocontainer():
    return taillog.gocontainer( request )
@app.route("/collectlog" , methods=['GET','POST'])
def collectlog():
    req = request.args
    return taillog.collectlog( req )
@app.route("/mon" , methods=['GET','POST'])
def mon():
    req = request.args
    return taillog.mon( req )
@app.route("/detaillog" , methods=['GET','POST'])
def detaillog():
    req = request.args
    return taillog.detaillog( req )
@app.route("/getdetaillog" , methods=['GET','POST'])
def getdetaillog():
    req = request.args
    return taillog.getdetaillog( req )
@app.route("/search" , methods=['GET','POST'])
def search():
    req = request.args
    return taillog.search( req )
@app.route("/execcmd" , methods=['GET','POST'])
def execcmd():
    req = request.args
    return taillog.execcmd( req )
@app.route("/report" , methods=['GET','POST'])
def report():
    req = request.args
    return taillog.report( req )
@app.route("/gettimelist" , methods=['GET','POST'])
def gettimelist():
    req = request.args
    return taillog.gettimelist( req )

@app.route('/tail', methods=['GET'])
def tail_html():
    req = request.args
    pod = req.get('pod')
    namespaces = req.get('namespaces')
    return render_template('tail.html',namespaces=namespaces , pod=pod)

@app.template_filter('to_float')
def to_float(value):
    try:
        return float(value)
    except ValueError:
        return value  # 如果转换失败，返回原始值

@app.route('/top', methods=['GET'])
def top_html():
    return render_template('top.html')


@app.route('/ps_aux', methods=['GET'])
def ps_aux_html():
    return render_template('ps_aux.html')

@socketio.on('connect', namespace="/shell")
def connect():
    print("connect..")

@socketio.on('disconnect', namespace="/shell")
def disconnect():
    print("disconnect..")

@socketio.on('client', namespace="/shell")
def client_info(data):
    print('client data', data)
    _type = data.get('_type')
    if _type == 'tail':
        cmd = "kubectl logs -f -n %s %s --tail 30"%( data.get('namespaces'), data.get('pod'))
        print(cmd)
        tail_command.background_thread(cmd)
    elif _type == 'ps':
        ps_command.background_thread()
    elif _type == 'top':
        top_command.background_thread()
    else:
        socketio.emit('response', {'text': '未知命令'}, namespace='/shell')

@socketio.on('leave', namespace="/shell")
def leave(data):
    print('leave data', data)
    _type = data.get('_type')
    if _type == 'tail':
        tail_command.leave()
    elif _type == 'ps':
        ps_command.leave()
    elif _type == 'top':
        top_command.leave()
    else:
        socketio.emit('response', {'text': '未知命令'}, namespace='/shell')
# WebSocket 路由处理
users = set()
@app.route('/websocket/')
def handle_websocket():
    wsock = request.environ.get('wsgi.websocket')
    users.add(wsock)
    if not wsock:
        abort(400, 'Expected WebSocket request.')

    while True:
        try:
            message = wsock.receive()  # 接收客户端信息
        except Exception as e:
            break
        print("现有连接用户：%s" % (len(users)))
        if message:
            for user in users:
                try:
                    user.send(message)  # 给所有客户端推送消息
                except Exception as e:
                    print('用户已断开连接')
    users.remove(wsock)
    
if __name__ == "__main__":
#    import platform
#    sysstr = platform.system()
#    if sysstr == 'Windows':
#        os.system("del /F /S /Q server\pid\*")
#    elif sysstr == 'Linux':
#        os.system("rm -f server/pid/*")
    filename = sys.argv[0]; 
    if len(sys.argv)< 2: 
        print ('usage: ' + filename + ' port' )
        sys.exit() 
    if len(sys.argv) > 2: 
        current_directory = sys.argv[2]
    port = sys.argv[1]
    app.config["WS_PORT"] = port
    socketio.run(app, host='0.0.0.0', port=port,debug=True)
#    app.run(threaded=True,host='0.0.0.0',port=port,debug=True)
