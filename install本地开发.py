#! /usr/bin/python3.6

# -*- coding: gbk -*-

import os
import subprocess
import sys

def ssh_set(ip):
    KEY_FILE="/root/.ssh/id_rsa"
    # Step 1: ����Ƿ��Ѿ��� SSH ��Կ�ԣ����û�о�����һ��
    if not os.path.isfile(KEY_FILE):
        os.system('ssh-keygen -t rsa -b 4096 -f "$KEY_FILE" -N "" -q')
    # Step 2: ����Կ���Ƶ�Զ�̷�����
    os.system('ssh-copy-id "root@%s"'%(ip))
    # Step 3: �����Ƿ�������ܵ�¼
    os.system(""" ssh "root@%s" "echo 'Passwordless SSH login successful!'" """%(ip))

def install_env(ip):
    cmd = "scp -r root@%s:/usr/local/java /usr/local/"%(ip)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.read()
    res = result.decode('utf-8')
    print("����java������ɣ�")
    cmd = "scp -r root@%s:/usr/local/apache-maven-3.6.3 /usr/local/"%(ip)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.read()
    res = result.decode('utf-8')
    print("����maven������ɣ�")
    cmd = "scp -r root@%s:/usr/sbin/logsvc /usr/sbin/ && chmod 777 /usr/sbin/logsvc"%(ip)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.read()
    res = result.decode('utf-8')
    print("����logsvc��ɣ�")
    cmd = "scp -r root@%s:/usr/local/tracelog /usr/local/"%(ip)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.read()
    res = result.decode('utf-8')
    print("����tracelog��ɣ�")
    cmd = "scp -r root@%s:/usr/sbin/run_project /usr/sbin/  && chmod 777 /usr/sbin/run_project"%(ip)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.read()
    res = result.decode('utf-8')
    cmd = "scp -r root@%s:/usr/sbin/reloadhosts /usr/sbin/  && chmod 777 /usr/sbin/reloadhosts"%(ip)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.read()
    res = result.decode('utf-8')
    print("����run_project��ɣ�")
    cmd = "scp -r root@%s:/usr/sbin/install_env /usr/sbin/  && chmod 777 /usr/sbin/install_env"%(ip)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.read()
    res = result.decode('utf-8')
    print("����install_env��ɣ�")
    os.system("pip3 install -i http://*************/repository/pypi-group/simple/ Flask --trusted-host *************")
    os.system("pip3 install -i http://*************/repository/pypi-group/simple/ --trusted-host ************* -r /usr/local/tracelog/requirements.txt")
    if os.path.exists("/root/.bash_profile"):
        with open("/root/.bash_profile",'r') as f:
            content = f.read()
        if content is not None:
            with open("/root/.bash_profile",'w') as f:
                f.write(content.replace("PATH=$PATH","PATH=/usr/local/java/bin:/usr/local/apache-maven-3.6.3/bin:$PATH"))
            cmd = "bash -c 'source /root/.bash_profile'"
            os.system(cmd)
            print("���û���������ɣ�")
    else:
        with open("/root/.bashrc",'r') as f:
            content = f.read()
        if content is not None:
            if "PATH=" in content:
                with open("/root/.bashrc",'w') as f:
                    f.write(content.replace("PATH=$PATH","PATH=/usr/local/java/bin:/usr/local/apache-maven-3.6.3/bin:$PATH"))
            else:
                with open("/root/.bashrc",'a') as f:
                    f.write("PATH=/usr/local/java/bin:/usr/local/apache-maven-3.6.3/bin:$PATH\nexport PATH")
            cmd = "bash -c 'source /root/.bashrc'"
            os.system(cmd)
            print("���û���������ɣ�")
    # curl http://************:8899/install_env | python3 - ************
    # nohup python3 -m http.server 8899 > /dev/null 2>&1 &
if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("�÷�: python install_env <ip>")
        sys.exit(1)
    ip = port = sys.argv[1]
    ssh_set(ip)
    install_env(ip)