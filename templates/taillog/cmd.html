
<html><head><style type='text/css'><!--body { line-height: 10pt; font-size: 10pt}--></style>
 <style type="text/css">
    *{margin: 0;padding: 0;}
     .nav{
         background-color: #232f3e;
        font-size: 14px;
        height: 50px;
        overflow: hidden;
        overflow-x: hidden;
        overflow-y: hidden;
         width: 100%;
         position: fixed;
         top: 0;
     }
     .content{
         height: 1400px;
         margin-top: 100px; 
     }
</style>
<script>
    function search(op){
        document.getElementById("form0").submit();
    }

    function set_page2(){
        document.getElementById("form1").submit();
        var PAGE_START=(parseInt(document.getElementById("PAGE_NUM2").value) - 1) * parseInt("{{pageSize or '5000'}}") + 1;
        location="/getlog?BOARD_ID=${BOARD_ID}&PAGE_START="  + PAGE_START + "&asc_desc=1&order_type="
    }
    function query(op){
        document.getElementById("form1").submit();
    }
</script>
</head>
<body bgcolor='#c0c0c0'>
<div class="nav">
    </br>
    <form name='form0' id='form0' action="/search" method="get">
        &nbsp;&nbsp;<a href='/pods'><font color = '#ffffff'>主页</font></a>&nbsp;&nbsp;
        <font color = '#ffffff'>关键字</font>:<input name="keyworld" id="keyworld" type="text" value="{{keyworld or ''}}" />
        &nbsp;&nbsp;<input  onClick="search()" type="button"  value="全局搜索" name="B1">
    </form>
    
</div>
    <div class="content">
        {% autoescape false %}
        {% for line in data_list %}
        {%if " ERROR " in line.decode('utf-8') %}
        <font color='#ff0000'>{{line.decode('utf-8').replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;') }}</font></br>
        {%elif " WARN " in line.decode('utf-8') %}
        <font color='#0000ff'>{{line.decode('utf-8').replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;')  }}</font></br>
        {%else%}
        <font color='#000000'>{{line.decode('utf-8').replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;').replace(keyworld,"<a href='/getdetaillog?file=%s&num=%s&keyworld=%s' TARGET = '_blank'>%s</a>"%(line.decode('utf-8').split(':')[0],line.decode('utf-8').split(':')[1],keyworld,keyworld))  }}</font></br>
        {% endif %}
        {% endfor %}
        {% endautoescape %}
    </div>
</body>

</html>