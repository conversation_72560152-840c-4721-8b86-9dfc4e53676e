<!DOCTYPE html>
<html>
<head>
<script language="javaScript">
    /*function b(){
    var b = document.getElementById("b");
    var value = b.options[b.selectedIndex].value; // 选中值
    box.style.backgroundColor = value;
    }

    function f(){
    var f = document.getElementById("f");
    var value = f.options[f.selectedIndex].value; // 选中值
    document.getElementById("box").style.fontSize = value;
    }
    function c(){
    var c = document.getElementById("c");
    var value = c.options[c.selectedIndex].value; // 选中值
    document.getElementById("box").style.color = value;
    }*/

    function change(idname,changewhereid,changewhat){	
        var idname = document.getElementById(idname);
        var value = idname.options[idname.selectedIndex].value; // 选中值
        // alert(value);
        // alert(changewhereid);
        // alert(changewhat);
        document.getElementById('msg').style[changewhat] = value;
    }

</script>	
</head>
    <style>
        html,body{
            height:100%;
            overflow: hidden;
        }
        #msg{
            width:100%; height: calc(100% - 60px); overflow:auto; border:2px solid #000000;background-color:#000000;color:#ffffff;list-style:none;
        }
    </style>
</head>
<body>
    <div class="all" id="all">
        {{pod}}的实时日志&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        背景颜色：<select name="" id="b" onchange = "change('b','all','background-color')">
        <option value="#000000" select="selected">默认</option>
        <option value="#ffffff">白</option>
        <option value="red">红</option>
        <option value="yellow">黄</option>
        <option value="blue">蓝</option>
        </select>
        字体大小：<select name="" id="f" onchange = "change('f','box','fontSize')">
        <option value="12px">默认</option>
        <option value="14px">14px</option>
        <option value="48px">48px</option>
        <option value="72px">72px</option>
        <option value="84px">84px</option>
        </select>
        字体颜色：<select name="" id="c" onchange = "change('c','box','color')">
        <option value="#ffffff">默认</option>
        <option value="#000000">黑</option>
        <option value="green">绿</option>
        <option value="red">红</option>
        <option value="yellow">黄</option>
        <option value="blue">蓝</option>
        </select>
    </div>
    </br>
    <div id="msg"></div>
    <script src="/static/jquery.min.js"></script>
    <script>
    $(document).ready(function() {
        /* !window.WebSocket、window.MozWebSocket检测浏览器对websocket的支持*/
        if (!window.WebSocket) {
            if (window.MozWebSocket) {
                window.WebSocket = window.MozWebSocket;
            } else {
                $('#msg').prepend("<p>你的浏览器不支持websocket</p>");
            }
        }
        /* ws = new WebSocket 创建WebSocket的实例  注意设置对以下的websocket的地址哦*/
        ws = new WebSocket('ws://{{ws_ip}}:{{ws_port}}/websocket/?pod={{pod}}');
//        ws = new WebSocket('ws://{{ws_ip}}:18000/echo/{{pod}}');
        /*
            ws.onopen  握手完成并创建TCP/IP通道，当浏览器和WebSocketServer连接成功后，会触发onopen消息
            ws.onmessage 接收到WebSocketServer发送过来的数据时，就会触发onmessage消息，参数evt中包含server传输过来的数据;
        */
        ws.onopen = function(evt) {
            $('#msg').append('<li>正在处理请稍等..........</li>');
        }
        ws.onmessage = function(evt) {
            var line = evt.data;
            if (line.indexOf("ERROR") != -1 ) {
                $('#msg').append('<li style="color:#F00;">' + line + '</li>');
            } else if (line.indexOf("WARN") != -1 ) {
                $('#msg').append('<li style="color:#00F;>' + line + '</li>');
            }  else {
                $('#msg').append('<li>' + line + '</li>');
            }
            
            $("#msg").scrollTop($("#msg")[0].scrollHeight);
        }
    });
</script>
</body>
</html>