<html><head>
<link rel="stylesheet" href="/static/all.min.css">
<style type="text/css">
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    .nav {
        background-color: #232f3e;
        font-size: 16px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-start;  /* 左对齐 */
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        padding-left: 20px;  /* 左侧留空 */
    }
    .nav form {
        display: flex;
        align-items: center;
        margin-right: 20px;  /* 控制表单之间的距离 */
    }
    .nav input[type="text"] {
        margin-left: 10px;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    .nav input[type="button"] {
        margin-left: 10px;
        padding: 5px 10px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .nav input[type="button"]:hover {
        background-color: #45a049;
    }
    body {
        background-color: #c0c0c0;
        font-family: Arial, sans-serif;
        color: #333;
    }    
    .content{
         background-color: #c0c0c0;
         height: 1400px;
         margin-top: 100px; 
         line-height: 1.5; 
     }
</style>

<script>
    function search(op){
        document.getElementById("form0").submit();
    }
    function execCmd(op){
        document.getElementById("form2").submit();
    }    
</script>
</head><body bgcolor='#c0c0c0'>
<div class="nav">
    </br>
    <form name='form0' id='form0' action="/search" method="get">
        &nbsp;&nbsp;<a href='/pods'><i class="fas fa-home" style="color: #ffffff;"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <font color = '#ffffff'>关键字</font>:<input name="keyworld" id="keyworld" type="text" value="{{keyworld or ''}}" />
        &nbsp;&nbsp;<input  onClick="search()" type="button"  value="全局搜索" name="B1">
    </form>
    <form name='form2' id='form2' action="/mon" method="get">
        <input name="namespaces" id="namespaces" type="hidden" value="{{namespaces or 'incloud'}}" />
        <input name="pod" id="pod" type="hidden" value="{{pod or ''}}" />
        <font color = '#ffffff'>模块</font>:<input name="module" id="module" type="text" value="{{module or ''}}" />
            &nbsp;&nbsp;<font color = '#ffffff'>请求方法</font>:
        <select name="method" id="method">
            <option value="">全部</option>  <!-- 如果不需要过滤请求方法，选择这个 -->
            <option value="GET" {% if method == 'GET' %}selected{% endif %}>GET</option>
            <option value="POST" {% if method == 'POST' %}selected{% endif %}>POST</option>
            <option value="PUT" {% if method == 'PUT' %}selected{% endif %}>PUT</option>
            <option value="DELETE" {% if method == 'DELETE' %}selected{% endif %}>DELETE</option>
        </select>
        &nbsp;&nbsp<font color = '#ffffff'>url</font>:<input name="url" id="url" type="text" value="{{url or ''}}" size="60"/>
        &nbsp;&nbsp;<input  onClick="execCmd()" type="button"  value="过滤" name="B1">
    </form>

</div>
<div class="content">
    {% for line in data_list %}
    {%if "ERROR" in line%}
    <font color='#ff0000'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;') }}</font></br>
    {%elif "WARN" in line%}
    <font color='#0000ff'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;')  }}</font></br>
    {%else%}
    {{line[0]}} {{line[1]}} <a href='/detaillog?pod={{pod}}&date={{line[0]}}&starttime={{line[1]}}&requestid={{line[2]}}' TARGET = "_blank">{{line[2]}}</a> {{line[3]}}&nbsp;&nbsp;<a href='/gettimelist?pod={{pod}}&date={{line[0]}}&starttime={{line[1]}}&requestid={{line[2]}}' TARGET = "_blank">耗时分析</a></br>
    {% endif %}
    {% endfor %}
</div>
</body></head></html>