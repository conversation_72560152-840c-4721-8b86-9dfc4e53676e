{% extends "taillog/base.html" %}

{% block content %}
  <body>
  <div align="center">
    <h1 align="center"><a href="" target="github">根据requestid统计请求在各个模块耗时情况，如果有些模块没开启 enable-invoke-time-print或者requestid中途被篡改则无法统计到。</a></h1>
    <table class="bordered">
      <tr>
        <td>
          <div id="hoursChart" style="height:400px; width: 1340px;"></div>
        </td>
      </tr>
    </table>
  </div>

  <script src="static/echarts.js"></script>
  <script type="text/javascript">
    var ec = echarts;
          var hoursChart = ec.init(document.getElementById('hoursChart'));

          var hoursOption = {
            title: {
              text: '每个模块耗时统计',
              subtext: ''
            },
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['模块名']
            },
            toolbox: {
              show: true,
              feature: {
                mark: {show: false},
                dataView: {show: false, readOnly: false},
                magicType: {show: true, type: ['line', 'bar']},
                restore: {show: false},
                saveAsImage: {show: true}
              }
            },
            calculable: true,
            xAxis: [
              {
                type: 'category',
                boundaryGap: true,
                data: [{% for hour in hours_times %}'{{ hour }}',{% endfor %}],
                axisLabel: {
                rotate:10,
                interal:0
                }
              }
            ],
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  formatter: '{value}'
                }
              }
            ],
            series: [
              {
                name: '耗时（毫秒）',
                type: 'bar',
                data: [{% for hour in hours_times %}{{ data[hour] }}, {% endfor %}],
                markPoint: {
                  data: [
                    {type: 'max', name: '最大值'},
                    {type: 'min', name: '最小值'}
                  ]
                },
                markLine: {
                  data: [
                    {type: 'average', name: '平均值'}
                  ]
                }
              }
            ]
          };
          hoursChart.setOption(hoursOption);

  </script>


  </body>
{% endblock %}
