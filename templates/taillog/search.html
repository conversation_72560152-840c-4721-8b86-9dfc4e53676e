
<html><head><style type='text/css'><!--body { line-height: 10pt; font-size: 10pt}--></style>
<link rel="stylesheet" href="/static/all.min.css">
 <style type="text/css">
    *{margin: 0;padding: 0;}
          .nav {
        background-color: #232f3e;
        font-size: 16px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-start;  /* 左对齐 */
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        padding-left: 20px;  /* 左侧留空 */
    }
    .nav form {
        display: flex;
        align-items: center;
        margin-right: 20px;  /* 控制表单之间的距离 */
    }
    .nav input[type="text"] {
        margin-left: 10px;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    .nav input[type="button"] {
        margin-left: 10px;
        padding: 5px 10px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    body {
        background-color: #c0c0c0;
        font-family: Arial, sans-serif;
        color: #333;
    }    
    .content{
         background-color: #c0c0c0;
         height: 1400px;
         margin-top: 100px; 
         line-height: 1.5; 
     }
</style>
<script>
    function search(op){
        document.getElementById("form0").submit();
    }

    function set_page2(){
        document.getElementById("form1").submit();
        var PAGE_START=(parseInt(document.getElementById("PAGE_NUM2").value) - 1) * parseInt("{{pageSize or '5000'}}") + 1;
        location="/getlog?BOARD_ID=${BOARD_ID}&PAGE_START="  + PAGE_START + "&asc_desc=1&order_type="
    }
    function query(op){
        document.getElementById("form1").submit();
    }
    function execCmd(op){
        debugger;
        document.getElementById("form2").submit();
    }    
</script>
</head>
<body bgcolor='#c0c0c0'>
<div class="nav">
    </br>
    <form name='form0' id='form0' action="/search" method="get">
        &nbsp;&nbsp;<a href='/pods'><i class="fas fa-home" style="color: #ffffff;"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <font color = '#ffffff'>关键字</font>:<input name="keyworld" id="keyworld" type="text" value="{{keyworld or ''}}" />
        &nbsp;&nbsp;<input  onClick="search()" type="button"  value="全局搜索" name="B1">
    </form>
    <form name='form2' id='form2' action="/execcmd" method="get">
        <input name="namespaces" id="namespaces" type="hidden" value="{{namespaces or 'incloud'}}" />
        &nbsp;&nbsp<font color = '#ffffff'>命令</font>:<input name="cmd" id="cmd" type="text" value="{{cmd or ''}}" size="60"/>
        &nbsp;&nbsp;<input  onClick="execCmd()" type="button"  value="执行命令" name="B1">
    </form>


</div>
    <div class="content">
        {% autoescape false %}
        {% for line in data_list %}
        {%if " ERROR " in line %}
        <font color='#ff0000'>{{line.replace(keyworld,"<a href='/getdetaillog?file=%s&num=%s&keyworld=%s&pageSize=200' TARGET = '_blank'>%s</a>"%(line.split(':')[0],line.split(':')[1],keyworld,keyworld)).replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;').replace(work_path + '/logs/','')  }}</font></br>
        {%elif " WARN " in line %}
        <font color='#e65100'>{{line.replace(keyworld,"<a href='/getdetaillog?file=%s&num=%s&keyworld=%s&pageSize=200' TARGET = '_blank'>%s</a>"%(line.split(':')[0],line.split(':')[1],keyworld,keyworld)).replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;').replace(work_path + '/logs/','')  }}</font></br>
        {%else%}
        <font color='#000000'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;').replace(work_path + '/logs/','').replace(keyworld,"<a href='/getdetaillog?file=%s&num=%s&keyworld=%s&pageSize=200' TARGET = '_blank'>%s</a>"%(line.split(':')[0],line.split(':')[1],keyworld,keyworld))  }}</font></br>
        {% endif %}
        {% endfor %}
        {% endautoescape %}
    </div>
</body>

</html>