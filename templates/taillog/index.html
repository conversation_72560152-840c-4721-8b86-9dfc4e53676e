<!DOCTYPE HTML>
<html>
<head>
<title>模块列表</title>
<link rel="stylesheet" href="/static/all.min.css">
<style type="text/css">
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    body {
        background-color: #f4f4f9;
        font-family: Arial, sans-serif;
        color: #333;
    }
    .nav {
        background-color: #232f3e;
        font-size: 16px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-start;  /* 左对齐 */
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        padding-left: 20px;  /* 左侧留空 */
    }
    .nav form {
        display: flex;
        align-items: center;
        margin-right: 20px;  /* 控制表单之间的距离 */
    }
    .nav input[type="text"] {
        margin-left: 10px;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    .nav input[type="button"] {
        margin-left: 10px;
        padding: 5px 10px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .nav input[type="button"]:hover {
        background-color: #45a049;
    }
    .content {
        margin-top: 80px;
        padding: 20px;
    }
    table {
        width: 80%;
        margin: 20px auto;
        border-collapse: collapse;
        background-color: white;
    }
    .content input[type="text"] {
        margin-left: 10px;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    .left-align {
        text-align: left;  /* NAME靠左对齐 */
    }
    th, td {
        padding: 12px;
        border: 1px solid #ddd;
        text-align: center;
    }
    th {
        background-color: #f2f2f2;
    }
    tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    tr:hover {
        background-color: #f1f1f1;
    }
    .button-style {
        background-color: #4CAF50;
        color: white;
        padding: 5px 10px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .button-style:hover {
        background-color: #45a049;
    }
</style>

<script>
    function restart(id){
        msg='确认要重启'+id;
        if(window.confirm(msg))
        {
            url="restart?pod="+id;
            location=url;
        }
    }
    function rebulid(id){
        msg='确认要重新构建'+id;
        if(window.confirm(msg))
        {
            url="rebuild?pod="+id;
            var tempwindow=window.open('_blank');
            tempwindow.location=url;
//            location=url;
        }
    }
    function gocontainer(id){
        msg='kubectl exec -it -n {{project}} '+id + ' sh';
        window.confirm(msg);
    }
    function query(op){
        document.getElementById("form1").submit();
    }
    function search(op){
        debugger;
        document.getElementById("form0").submit();
    }
    function execCmd(op){
        debugger;
        document.getElementById("form2").submit();
    }
</script>
</head>
<body bgcolor='#c0c0c0'>
<div class="nav">
    </br>
    <form name='form0' id='form0' action="/search" method="get">
        &nbsp;&nbsp;<a href='/pods'><i class="fas fa-home" style="color: #ffffff;"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <font color = '#ffffff'>关键字</font>:<input name="keyworld" id="keyworld" type="text" value="{{keyworld or ''}}" />
        <input name="namespaces" id="namespaces" type="hidden" value="{{namespaces or 'incloud'}}" />
        &nbsp;&nbsp;<input  onClick="search()" type="button"  value="全局搜索" name="B1">
    </form>
    <form name='form2' id='form2' action="/execcmd" method="get">
        <input name="namespaces" id="namespaces" type="hidden" value="{{namespaces or 'incloud'}}" />
        &nbsp;&nbsp<font color = '#ffffff'>命令</font>:<input name="cmd" id="cmd" type="text" value="{{cmd or ''}}" size="60"/>
        &nbsp;&nbsp;<input  onClick="execCmd()" type="button"  value="执行命令" name="B1">
    </form>
    
</div>
<div class="content">
{% if hours_times | length > 0 %}
    <table class="bordered">
      <tr>
        <td>
          <div id="hoursChart" style="height:200px;" width="65%"></div>
        </td>
      </tr>
    </table>
{% endif %}
    <table border="1" class="TableBlock" width="65%" align="center">
        <!-- 表头 -->
        <thead>
            <!-- 搜索表单行 -->
            <tr>
                <td colspan="8" class="left-align">
                    <form name='form1' id='form1' action="/pods" method="get">
                        NAME:
                        <input name="name" id="name" type="text" value="{{name or ''}}" />
                        &nbsp;&nbsp; NAMESPACE：
                        <input name="namespaces" id="namespaces" type="text" value="{{namespaces or 'incloud'}}" />
                        &nbsp;&nbsp;
                        <input onClick="query()" type="button" value="查询" name="B1" class="button-style">
                    </form>
                </td>
            </tr>
            <tr>
                <th width="450">NAME</th>
                <th width="90">READY</th>
                <th width="100">STATUS</th>
                <th width="130">RESTARTS</th>
                <th width="100">AGE</th>
                <th width="100">IP</th>
                <th width="100">NODE</th>
                <th width="300">操作</th>
            </tr>
        </thead>
        <!-- 表格内容 -->
        <tbody>
            {%for row in data_list%}
            <tr>
                <td><a title="日志" href="/getlog?pod={{row[0]}}" target="_blank">{{row[0]}}</a></td>
                <td align="center">{{row[1]}}</td>
                {%if row[2]=="Running"%}
                <td align="center"><font color='#008000'>{{row[2]}}</font></td>
                {%else%}
                <td align="center"><font color='#ff0000'>{{row[2]}}</font></td>
                {%endif%}
                <td align="center">{{row[3]}}</td>
                <td align="center">{{row[-5]}}</td>
                <td align="center">{{row[-4]}}</td>
                <td align="center">{{row[-3]}}</td>
                <td align="center">
                    <a title="日志" href="/getlog?pod={{row[0]}}&namespaces={{namespaces or 'incloud'}}" target="_blank">日志</a>&nbsp;&nbsp;&nbsp;
                    <a title="日志收集" href="/collectlog?pod={{row[0]}}&namespaces={{namespaces or 'incloud'}}">日志收集</a>&nbsp;&nbsp;&nbsp;
                    <a title="日志索引" href="/mon?pod={{row[0]}}&namespaces={{namespaces or 'incloud'}}" target="_blank">日志索引</a>&nbsp;&nbsp;&nbsp;
                    <a title="实时日志" href="/tail?pod={{row[0]}}&namespaces={{namespaces or 'incloud'}}" target="_blank">实时日志</a>&nbsp;&nbsp;&nbsp;
                    <a title="分析报告" href="/report?pod={{row[0]}}&namespaces={{namespaces or 'incloud'}}" target="_blank">分析报告</a>&nbsp;&nbsp;&nbsp;
                </td>
            </tr>
            {%endfor%}
        </tbody>
    </table>
</div>
  <script src="static/echarts.js"></script>
  <script type="text/javascript">
  {% if hours_times | length > 0 %}
    var ec = echarts;
          var hoursChart = ec.init(document.getElementById('hoursChart'));

          var hoursOption = {
            title: {
              text: '日志大小',
              subtext: ''
            },
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['模块']
            },
            toolbox: {
              show: true,
              feature: {
                mark: {show: false},
                dataView: {show: false, readOnly: false},
                magicType: {show: true, type: ['line', 'bar']},
                restore: {show: false},
                saveAsImage: {show: true}
              }
            },
            calculable: true,
            xAxis: [
              {
                type: 'category',
                boundaryGap: true,
                data: [{% for hour in hours_times %}'{{ hour }}',{% endfor %}],
                axisLabel: {
                rotate:10,
                interal:0
                }
              }
            ],
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  formatter: '{value}'
                }
              }
            ],
            series: [
              {
                name: '大小（MB）',
                type: 'bar',
                data: [{% for hour in hours_times %}{{ data[hour] }}, {% endfor %}],
                markPoint: {
                  data: [
                    {type: 'max', name: '最大值'},
                    {type: 'min', name: '最小值'}
                  ]
                },
                markLine: {
                  data: [
                    {type: 'average', name: '平均值'}
                  ]
                }
              }
            ]
          };
          hoursChart.setOption(hoursOption);
  {% endif %}

  </script>

</body>
</html>