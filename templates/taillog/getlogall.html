<html>
<head>
    <style type='text/css'>
        body { line-height: 10pt; font-size: 10pt; }
    </style>
<link rel="stylesheet" href="/static/all.min.css">
<style type="text/css">
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    .nav {
        background-color: #232f3e;
        font-size: 16px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-start;  /* 左对齐 */
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        padding-left: 20px;  /* 左侧留空 */
    }
    .nav form {
        display: flex;
        align-items: center;
        margin-right: 20px;  /* 控制表单之间的距离 */
    }
    .nav input[type="text"] {
        margin-left: 10px;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    .nav input[type="button"] {
        margin-left: 10px;
        padding: 5px 10px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .nav input[type="button"]:hover {
        background-color: #45a049;
    }
    body {
        background-color: #c0c0c0;
        font-family: Arial, sans-serif;
        color: #333;
    }    
    .content{
         background-color: #c0c0c0;
         height: 1400px;
         margin-top: 100px; 
         line-height: 1.5; 
     }
</style>

    <script src="/static/jquery.min.js"></script>
 <script>
    var isLoading = false;  // 防止重复加载
    var currentNum = {{num or '1'}};  // 初始页码
    var currentStart = {{start or '1'}};  // 初始开始位置
    var currentEnd = {{end or '1'}};  // 初始结束位置

    function loadMoreBottom() {
        if (isLoading) return;  // 防止重复加载
        isLoading = true;  // 标志开始加载

        $.get("/getlog", {
            keyworld: "{{keyworld or ''}}",
            num: currentNum,
            start: currentStart,
            end: currentEnd,
            loglevel: "{{loglevel or ''}}",
            start_time: "{{start_time or ''}}",
            end_time: "{{end_time or ''}}",
            search_name: "{{search_name or ''}}",
            pod: "{{pod or ''}}",
            flag: "down",
            pageSize: 50  // 每次加载的条数
        }, function(data) {
            $("#log-container").append(data.html);  // 将更多的日志追加到底部
            isLoading = false;  // 加载结束，允许新的请求

            // 更新分页参数，准备加载下一页数据
            currentNum = data.num;  // 更新为新的页码
            currentStart = data.start;  // 更新开始位置
            currentEnd = data.end;  // 更新结束位置
        });
    }

    $(window).on('scroll', function() {
        // 判断是否滚动到底部
        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 10) {
            loadMoreBottom();  // 调用函数加载更多内容
        }
    });
    
    function execCmd(op){
        document.getElementById("form2").submit();
    }   
</script>
</head>

<body bgcolor='#c0c0c0'>
    <div class="nav">
        </br>
        <form name='form0' id='form0' action="/search" method="get">
        &nbsp;&nbsp;<a href='/pods'><i class="fas fa-home" style="color: #ffffff;"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <font color = '#ffffff'>关键字</font>:<input name="keyworld" id="keyworld" type="text" value="{{keyworld or ''}}" />
            &nbsp;&nbsp;<input onClick="search()" type="button" value="全局搜索" name="B1">
        </form>
        <form name='form2' id='form2' action="/getlog" method="get">
            <input name="namespaces" id="namespaces" type="hidden" value="{{namespaces or 'incloud'}}" />
            <input name="pod" id="pod" type="hidden" value="{{pod or ''}}" />
             &nbsp;&nbsp;<font color = '#ffffff'>日志级别</font>:
            <select name="loglevel" id="loglevel">
                <option value="">全部</option>  <!-- 如果不需要过滤请求方法，选择这个 -->
                <option value="ERROR" {% if loglevel == 'ERROR' %}selected{% endif %}>异常</option>
                <option value="INFO" {% if loglevel == 'INFO' %}selected{% endif %}>正常</option>
            </select>
            <!-- 添加开始时间和结束时间 -->
            &nbsp;&nbsp;<font color = '#ffffff'>开始时间</font>:<input name="start_time" id="start_time" type="datetime-local" value="{{ start_time or '' }}" />
            &nbsp;&nbsp;<font color = '#ffffff'>结束时间</font>:<input name="end_time" id="end_time" type="datetime-local" value="{{ end_time or ''}}" />
            &nbsp;&nbsp;<font color = '#ffffff'>过滤字段</font>:<input name="search_name" id="search_name" type="text" value="{{search_name or ''}}" size="40" />
            &nbsp;&nbsp;<input onClick="execCmd()" type="button" value="过滤" name="B1">
        </form>
    </div>

    <div class="content">
        <div id="log-container">
            {% autoescape false %}
            {% for line in data_list %}
            {% if " ERROR " in line.decode('utf-8') %}
            <font color='#ff0000'>{{line.decode('utf-8').replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;') }}</font></br>
            {% elif " WARN " in line.decode('utf-8') %}
            <font color='#0000ff'>{{line.decode('utf-8').replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;')  }}</font></br>
            {% else %}
            <font color='#000000'>{{line.decode('utf-8').replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;')  }}</font></br>
            {% endif %}
            {% endfor %}
            {% endautoescape %}
        </div>
        <!-- 下部加载更多区域，这里改为自动触发加载，无需按钮 -->
        <div class="load-more-down" id="load-more-down"></div>
    </div>
</body>
</html>
