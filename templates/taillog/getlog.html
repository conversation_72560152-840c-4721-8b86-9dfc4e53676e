
<html><head><style type='text/css'><!--body { line-height: 10pt; font-size: 10pt}--></style>
<link rel="stylesheet" href="/static/all.min.css">
 <style type="text/css">
    *{margin: 0;padding: 0;}
    .nav {
        background-color: #232f3e;
        font-size: 16px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-start;  /* 左对齐 */
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        padding-left: 20px;  /* 左侧留空 */
    }
    .nav form {
        display: flex;
        align-items: center;
        margin-right: 20px;  /* 控制表单之间的距离 */
    }
    .nav input[type="text"] {
        margin-left: 10px;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    .nav input[type="button"] {
        margin-left: 10px;
        padding: 5px 10px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .nav input[type="button"]:hover {
        background-color: #45a049;
    }
    body {
        background-color: #c0c0c0;
        font-family: Arial, sans-serif;
        color: #333;
    }    
    .content{
         background-color: #c0c0c0;
         height: 1400px;
         margin-top: 100px; 
         line-height: 1.5; 
     }
</style>
<script>
    function search(op){
        document.getElementById("form0").submit();
    }
    function execCmd(op){
        document.getElementById("form2").submit();
    }    
</script>
</head>
<body bgcolor='#c0c0c0'>
    <div class="nav">
    </br>
    <form name='form0' id='form0' action="/search" method="get">
        &nbsp;&nbsp;<a href='/pods'><i class="fas fa-home" style="color: #ffffff;"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <font color = '#ffffff'>关键字</font>:<input name="keyworld" id="keyworld" type="text" value="{{keyworld or ''}}" />
        &nbsp;&nbsp;<input  onClick="search()" type="button"  value="全局搜索" name="B1">
    </form>
        
    </div>
    <div class="content">
        {% autoescape false %}
        {% for line in data_list %}
        {%if " ERROR " in line %}
        <font color='#ff0000'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;') }}</font></br>
        {%elif " WARN " in line %}
        <font color='#0000ff'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;')  }}</font></br>
        {%else%}
        <font color='#000000'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;')  }}</font></br>
        {% endif %}
        {% endfor %}
        {% endautoescape %}
    </div>

</body>

</html>