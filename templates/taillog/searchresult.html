
<html><head><style type='text/css'><!--body { line-height: 10pt; font-size: 10pt}--></style>
<link rel="stylesheet" href="/static/all.min.css">
 <style type="text/css">
    *{margin: 0;padding: 0;}
     .nav {
        background-color: #232f3e;
        font-size: 16px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-start;  /* 左对齐 */
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        padding-left: 20px;  /* 左侧留空 */
    }
    .nav form {
        display: flex;
        align-items: center;
        margin-right: 20px;  /* 控制表单之间的距离 */
    }
    .nav input[type="text"] {
        margin-left: 10px;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    .nav input[type="button"] {
        margin-left: 10px;
        padding: 5px 10px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .nav input[type="button"]:hover {
        background-color: #45a049;
    }
     .load-more-down {
            text-align: center;
            padding: 10px;
     }
      .load-more-up {
            text-align: center;
            padding: 10px;
     }
     body {
        background-color: #c0c0c0;
        font-family: Arial, sans-serif;
        color: #333;
    }    
    .content{
         background-color: #c0c0c0;
         height: 1400px;
         margin-top: 100px; 
         line-height: 1.5; 
     }
</style>
<script src="/static/jquery.min.js"></script>
<script>
    function search(op){
        document.getElementById("form0").submit();
    }

//    // 上部加载更多日志 /getdetaillog?file=/root/.tmp/tracelog/logs/istorage-6c6899fb95-2nw4k/daily/istorage-service.log.2024-10-11&num=17840&keyworld=v1/thrid-stor/licenses&pageSize=400
//    function loadMoreTop(currentNum,currentStart,currentEnd) {
//        $.get("/getdetaillog", { keyworld: "{{keyworld or ''}}",num: currentNum ,start: currentStart,end: currentEnd,file: "{{file or ''}}",flag: "up", pageSize: 50 }, function(data) {
//            $("#log-container").prepend(data.html);  // 将更多的日志追加到顶部
//            $("#load-more-up").html('<button onclick="loadMoreTop(' + data.num + ', ' + data.start + ', ' + data.end + ')">加载更多 (上)</button>'); 
//        });
//    }
    var isUpLoading = false;  // 防止重复加载
    var currentUpNum = {{num or '1'}};  // 初始页码
    var currentUpStart = {{start or '1'}};  // 初始开始位置
    var currentUpEnd = {{end or '1'}};  // 初始结束位置
    // 向上加载更多
    function loadMoreTop() {
        if (isUpLoading) return;  // 防止重复加载
        isUpLoading = true;  // 标志开始加载
        // 记录当前的滚动条位置和容器高度
        var scrollTopBeforeLoad = $(window).scrollTop();
        var containerHeightBeforeLoad = $("#log-container").height();
        $.get("/getdetaillog", {
            keyworld: "{{keyworld or ''}}",
            num: currentUpNum,
            start: currentUpStart,
            end: currentUpEnd,
            file: "{{file or ''}}",
            flag: "up",
            pageSize: 50  // 每次加载的条数
        }, function(data) {
            $("#log-container").prepend(data.html);  // 将更多的日志追加到顶部
            isUpLoading = false;  // 加载结束，允许新的请求
            // 加载完内容后，调整滚动条位置，使得视图保持不变
            var containerHeightAfterLoad = $("#log-container").height();
            var heightDifference = containerHeightAfterLoad - containerHeightBeforeLoad;
            $(window).scrollTop(scrollTopBeforeLoad + heightDifference);

            isUpLoading = false;  // 加载结束，允许新的请求

            // 更新分页参数，准备加载上一页数据
            currentUpNum = data.num;  // 更新为新的页码
            currentUpStart = data.start;  // 更新开始位置
            currentUpEnd = data.end;  // 更新结束位置
        });
    }
    
    var isLoading = false;  // 防止重复加载
    var currentNum = {{num or '1'}};  // 初始页码
    var currentStart = {{start or '1'}};  // 初始开始位置
    var currentEnd = {{end or '1'}};  // 初始结束位置

    function loadMoreBottom() {
        if (isLoading) return;  // 防止重复加载
        isLoading = true;  // 标志开始加载

        $.get("/getdetaillog", {
            keyworld: "{{keyworld or ''}}",
            num: currentNum,
            start: currentStart,
            end: currentEnd,
            file: "{{file or ''}}",
            flag: "down",
            pageSize: 50  // 每次加载的条数
        }, function(data) {
            $("#log-container").append(data.html);  // 将更多的日志追加到底部
            isLoading = false;  // 加载结束，允许新的请求

            // 更新分页参数，准备加载下一页数据
            currentNum = data.num;  // 更新为新的页码
            currentStart = data.start;  // 更新开始位置
            currentEnd = data.end;  // 更新结束位置
        });
    }
    // 页面加载时滚动到中间位置
    function scrollToMiddle() {
        var middlePosition = $(document).height() / 2 - $(window).height() / 2;
        $(window).scrollTop(middlePosition);  // 滚动到中间
    }

    // 页面完全加载后自动滚动到中间
    $(document).ready(function() {
        scrollToMiddle();  // 页面加载时滚动到中间
    });
    $(window).on('scroll', function() {
        // 判断是否滚动到底部
        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 10) {
            loadMoreBottom();  // 调用函数加载更多内容
        }
        // 判断是否滚动到顶部，加载更多内容
        if ($(window).scrollTop() <= 30) {
        debugger;
            loadMoreTop();  // 调用函数加载更多内容
        }
    });
</script>
</head>
<body bgcolor='#c0c0c0'>
<div class="nav">
    </br>
    <form name='form0' id='form0' action="/search" method="get">
        &nbsp;&nbsp;<a href='/pods'><i class="fas fa-home" style="color: #ffffff;"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <font color = '#ffffff'>关键字</font>:<input name="keyworld" id="keyworld" type="text" value="{{keyworld or ''}}" />
        &nbsp;&nbsp;<input  onClick="search()" type="button"  value="全局搜索" name="B1">
    </form>
    
</div>
    <div class="content">
        <!-- 上部加载更多按钮 
        <div class="load-more-up" id="load-more-up">
            <button onclick="loadMoreTop({{num or '1'}},{{start or '1'}},{{end or '1'}})">加载更多 (上)</button>
        </div>
        -->
        <div class="load-more-up" id="load-more-up"></div>

        <!-- 显示日志的容器 -->
        <div id="log-container">
            {% autoescape false %}
            {% for line in data_list %}
            {% if " ERROR " in line %}
            <font color='#ff0000'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;').replace(keyworld, '<span style="background-color: yellow;">' + keyworld + '</span>') | safe  }}</font></br>
            {% elif " WARN " in line %}
            <font color='#0000ff'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;').replace(keyworld, '<span style="background-color: yellow;">' + keyworld + '</span>') | safe   }}</font></br>
            {% else %}
            <font color='#000000'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;').replace(keyworld, '<span style="background-color: yellow;">' + keyworld + '</span>') | safe  }}</font></br>
            {% endif %}
            {% endfor %}
            {% endautoescape %}
        </div>

        <!-- 下部加载更多区域，这里改为自动触发加载，无需按钮 -->
        <div class="load-more-down" id="load-more-down"></div>
    </div>
</body>

</html>