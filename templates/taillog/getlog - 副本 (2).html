
<html><head><style type='text/css'><!--body { line-height: 10pt; font-size: 10pt}--></style>
 <style type="text/css">
    *{margin: 0;padding: 0;}
     .nav{
         background-color: #232f3e;
        font-size: 14px;
        height: 50px;
        overflow: hidden;
        overflow-x: hidden;
        overflow-y: hidden;
         width: 100%;
         position: fixed;
         top: 0;
     }
     .content{
         height: 1400px;
         margin-top: 100px; 
         line-height: 1.5; 
     }
</style>
<script>
    function set_page2(){
        document.getElementById("form1").submit();
        var PAGE_START=(parseInt(document.getElementById("PAGE_NUM2").value) - 1) * parseInt("{{pageSize or '5000'}}") + 1;
        location="/getlog?BOARD_ID=${BOARD_ID}&PAGE_START="  + PAGE_START + "&asc_desc=1&order_type="
    }
    function query(op){
        document.getElementById("form1").submit();
    }
</script>
</head>
<body bgcolor='#c0c0c0'>
    <div class="nav">
        </br>
        <a href='/pods'><font color = '#ffffff'>主页</font></a>&nbsp;&nbsp;
        <form name='form1' id='form1' action="/getlog" method="get">
            <font color = '#ffffff'>名称</font>:<input name="name" id="name" type="text" value="{{name or ''}}" />
            <font color = '#ffffff'>名称</font>:<input name="name" id="name" type="text" value="{{name or ''}}" />
            &nbsp;&nbsp;<input  onClick="query()" type="button"  value="查询" name="B1">
        </form>
        <a href="/getlog?BOARD_ID=${BOARD_ID}&asc_desc=1&order_type=" class="btn" disabled >首页</a>
        <a href="/getlog?BOARD_ID=${BOARD_ID}&PAGE_START=${page-1}&asc_desc=1&order_type=" class="btn" disabled >上一页</a>
        <a href="/getlog?BOARD_ID=${BOARD_ID}&PAGE_START=${page+1}&asc_desc=1&order_type=" class="btn" disabled >下一页</a>
        <a href="/getlog?BOARD_ID=${BOARD_ID}&PAGE_START=${page_total}&asc_desc=1&order_type=" class="btn" disabled>末页</a>
        <span style="float: left;font-size:12px;margin-right:5px;">页数</span>
        <input type="text" id="PAGE_NUM2" name="PAGE_NUM2" value="${page}" class="input-small" size="${page_total}" style="float: left;margin-top: 3px;width:50px;">
        <a href="javascript:set_page2();" title="转到指定的页面" class="btn">转到</a>
        
        
    </div>
    <div class="content">
        {% autoescape false %}
        {% for line in data_list %}
        {%if " ERROR " in line %}
        <font color='#ff0000'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;') }}</font></br>
        {%elif " WARN " in line %}
        <font color='#0000ff'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;')  }}</font></br>
        {%else%}
        <font color='#000000'>{{line.replace('\t','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;').replace(' ','&nbsp;&nbsp;')  }}</font></br>
        {% endif %}
        {% endfor %}
        {% endautoescape %}
    </div>

</body>

</html>