<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script src="/static/jquery.min.js"></script>
    <script type="text/javascript" src="/static/socket.io.min.js"></script>

    <script type="text/javascript" charset="utf-8">

        $(document).ready(function () {
            var _type = 'tail';
            var child_num = 0;
            var namespace = '/shell';
            var socket = io.connect(location.protocol + '//' + document.domain + ':' + location.port + namespace);
            socket.on('response', function (res) {
                console.log(res._type,_type,res._type === _type);
                debugger;
                if (res._type === _type) {
                    let formattedText;
                    // 将空格替换为 &nbsp;
                    let escapedText = res.text.replace(/ /g, '&nbsp;');

                    // 检查日志类型，设置不同的颜色
                    if (escapedText.includes('ERROR')) {
                        formattedText = `<span style="color: red;">${escapedText}</span>`;  // 错误日志用红色
                    } else if (escapedText.includes('WARN')) {
                        formattedText = `<span style="color: orange;">${escapedText}</span>`;  // 警告日志用橙色
                    } else if (escapedText.includes('INFO')) {
                        formattedText = `<span style="color: green;">${escapedText}</span>`;  // 信息日志用绿色
                    } else {
                        formattedText = `<span>${escapedText}</span>`;  // 其他日志保持默认颜色
                    }
                    if (child_num < 400) {
                        $('#terminal').append('<div>' + formattedText + '</div>');
                        child_num += 1
                    } else {
                        // 先删后加
                        $('#terminal div:first').remove();
                        $('#terminal').append('<div>' + formattedText + '</div>');
                    }
                    $(document).scrollTop($(document).height()); // 将滚动条滚到最下方
                    console.log(res.text);
                }
            });

            socket.emit('client', {'_type': _type,'namespaces': "{{namespaces or ''}}",'pod': "{{pod or ''}}"});

            $(window).bind('beforeunload', function () {    // 离开页面前关闭tail
                    socket.emit('leave', {'_type': _type});
                }
            );

        });

        $(window).resize(function () {
            var cliWidth = document.body.clientWidth;
            var cliHeight = document.body.clientHeight;
            var divWidth = cliWidth - 2;
            var divHeight = cliHeight - 2;
            $('#terminal').css("width", divWidth + "px");
            $('#terminal').css("height", divHeight + "px");
            $(document).scrollTop($(document).height()); // 将滚动条滚到最下方
        })

    </script>
    <style>
        html, body {
            height: 100%;
            margin: 0;
        }

        .outer {
            height: 100%;
        }

        #terminal {
            height: 100%;
            background-color: black;
            color: white;
            padding-left: 10px;
        }

        #terminal div {
            background-color: black;
            color: white;
        }
    </style>

</head>

<body>


<div class="outer">

    <div id="terminal">


    </div>
</div>


</body>


</html>