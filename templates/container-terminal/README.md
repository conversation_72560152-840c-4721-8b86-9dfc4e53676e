# Kubernetes 日志管理和容器监控平台

一个基于 Flask 的 Web 应用，提供 Kubernetes 集群中 Pod 的日志管理、实时监控和容器终端功能。

## 功能特性

### 🔍 Pod 管理
- Pod 列表查询和筛选（按名称、命名空间）
- Pod 重启和重新构建
- 容器状态监控

### 📊 日志分析
- **日志收集**: 自动收集 Pod 日志文件
- **实时日志**: WebSocket 实时日志流
- **日志索引**: 基于 requestid 的日志检索
- **分析报告**: 请求统计、性能分析、耗时分析
- **日志搜索**: 关键词搜索和高亮显示

### 🖥️ 系统监控
- **实时终端**: 集成 Kubernetes 容器终端
- **系统监控**: top 命令实时输出
- **进程监控**: ps aux 进程列表
- **命令执行**: 远程命令执行功能

### 📈 可视化报表
- ECharts 图表展示
- 请求量时间分布
- 性能指标统计
- 模块耗时分析

## 技术栈

- **后端**: Python Flask + SocketIO
- **前端**: HTML + JavaScript + ECharts
- **容器**: Kubernetes 集成
- **终端**: xterm.js + Angular (kubernetes-container-terminal)
- **构建**: Maven + Docker

## 项目结构

```
├── flask_ser.py              # Flask 主应用
├── taillog.py               # 日志处理核心模块
├── templates/               # 模板文件
│   ├── taillog/            # 日志相关页面
│   │   ├── index.html      # Pod 列表页面
│   │   ├── mon.html        # 日志索引页面
│   │   ├── report.html     # 分析报告页面
│   │   └── search.html     # 日志搜索页面
│   ├── container-terminal/ # 容器终端组件
│   ├── tail.html          # 实时日志页面
│   └── top.html           # 系统监控页面
├── static/                 # 静态资源
└── logparse/              # 日志解析模块
```

## 安装部署

### 环境要求
- Python 3.6+
- Flask
- Flask-SocketIO
- Kubernetes 集群访问权限
- kubectl 命令行工具

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd kubernetes-log-platform
```

2. **安装依赖**
```bash
pip install flask flask-socketio
```

3. **配置环境**
```bash
# 设置工作路径
export WORK_PATH=/path/to/logs
```

4. **启动应用**
```bash
python flask_ser.py
```

## 使用说明

### Pod 管理
1. 访问 `/pods` 查看 Pod 列表
2. 使用搜索框按名称或命名空间筛选
3. 点击操作链接进行日志查看、重启等操作

### 日志功能
- **日志收集**: 点击"日志收集"自动收集 Pod 日志
- **实时日志**: 点击"实时日志"查看 WebSocket 实时流
- **日志索引**: 点击"日志索引"按 requestid 检索
- **分析报告**: 点击"分析报告"查看性能统计

### 容器终端
集成 kubernetes-container-terminal 组件，提供：
- 容器内命令执行
- 交互式 Shell 访问
- 多容器支持

## API 接口

| 路由 | 方法 | 功能 |
|------|------|------|
| `/pods` | GET | Pod 列表查询 |
| `/restart` | GET | 重启 Pod |
| `/rebuild` | GET | 重新构建 Pod |
| `/getlog` | GET | 获取日志文件 |
| `/collectlog` | GET | 收集日志 |
| `/tail` | GET | 实时日志页面 |
| `/mon` | GET | 日志索引 |
| `/report` | GET | 分析报告 |
| `/search` | GET | 日志搜索 |

## 配置说明

### 环境变量
- `WORK_PATH`: 日志文件存储路径
- `PROJECT`: 默认 Kubernetes 命名空间

### 日志路径规则
```
{WORK_PATH}/logs/{pod_name}/{service_name}.log
```

## 开发说明

### 容器终端组件
基于 kubernetes-container-terminal 3.0.1：
- Angular 1.3.8+ 依赖
- xterm.js 3.12.0 终端模拟
- WebSocket 连接支持

### 构建流程
Maven + Docker 自动化构建：
1. `mvn deploy -DskipTests`
2. `mvn docker:build`
3. Docker 镜像推送
4. Kubernetes 部署更新

## 许可证

LGPL-2.1-or-later

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。
