<!doctype html>
<html class="no-js">
<head>
    <meta charset="utf-8">
    <title>Container Terminal Example</title>
    <meta name="viewport" content="width=device-width">
    <style>
        * { box-sizing: border-box; }
        body { margin: 20px !important; font-family: sans-serif; }
        label { display: block !important; }
        label span { float: left; width: 100px; margin-top: 2px; }
        label .form-control { display: inline !important; width: 300px; }
        body > div { margin-top: 15px; }
        kubernetes-container-terminal { }
    </style>
    <!--link rel="stylesheet" href="bower_components/patternfly/dist/css/patternfly.css">
    <link rel="stylesheet" href="bower_components/patternfly/dist/css/patternfly-additions.css"-->
    <link rel="stylesheet" href="/static/container-terminal/dist/xterm.css">
    <link rel="stylesheet" href="/static/container-terminal/container-terminal.css">
    <script src="/static/container-terminal/bower_components/angular/angular.js"></script>
    <script src="/static/container-terminal/dist/xterm.js"></script>
    <script src="/static/container-terminal/container-terminal.js"></script>
    <script src="/static/container-terminal/bower_components/jquery.js"></script>
    <script language="javaScript">
        function change(idname,changewhereid,changewhat){	
            var idname = document.getElementById(idname);
            var value = idname.options[idname.selectedIndex].value; // 选中值
//            document.getElementById('xterm-viewport').style[changewhat] = value;
            document.getElementsByClassName('xterm-screen')[0].style[changewhat] = value;
        }
    </script>	
</head>
<body ng-app="exampleApp">
    <div class="all" id="all">
        {{pod}}的控制台&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        背景颜色：<select name="" id="b" onchange = "change('b','all','background-color')">
        <option value="#000000" select="selected">默认</option>
        <option value="#ffffff">白</option>
        <option value="red">红</option>
        <option value="yellow">黄</option>
        <option value="blue">蓝</option>
        </select>
        字体大小：<select name="" id="f" onchange = "change('f','box','fontSize')">
        <option value="12px">默认</option>
        <option value="14px">14px</option>
        <option value="48px">48px</option>
        <option value="72px">72px</option>
        <option value="84px">84px</option>
        </select>
        字体颜色：<select name="" id="c" onchange = "change('c','box','color')">
        <option value="#ffffff">默认</option>
        <option value="#000000">黑</option>
        <option value="green">绿</option>
        <option value="red">红</option>
        <option value="yellow">黄</option>
        <option value="blue">蓝</option>
        </select>
    </div>
    </br>
    <kubernetes-container-terminal pod="selfLink" container="containerName" prevent="preventSocket" rows="rows" cols="cols"  screen-keys="true" autofocus="true">
    </kubernetes-container-terminal>
<!--
    <div>
        <label>
            <span>Endpoint</span>
            <input type="text" class="form-control" ng-model="baseUrl"/>
        </label>
        <label>
            <span>Pod link</span>
            <input type="text" class="form-control" ng-model="selfLink"/>
        </label>
        <label>
            <span>Container</span>
            <input type="text" class="form-control" ng-model="containerName"/>
        </label>
        <label>
            <span>Access Token</span>
            <input type="text" class="form-control" ng-model="accessToken"/>
        </label>
        <label>
            <span>Rows</span>
            <input type="text" class="form-control" ng-model="rows"/>
        </label>
        <label>
            <span>Cols</span>
            <input type="text" class="form-control" ng-model="cols"/>
        </label>
    </div>
-->
    <script type="text/javascript">
        angular.module('exampleApp', ['kubernetesUI'])
            .config(function(kubernetesContainerSocketProvider) {
                kubernetesContainerSocketProvider.WebSocketFactory = "CustomWebSockets";
            })

            .run(function($rootScope) {
                $rootScope.baseUrl = "{{baseUrl}}";
                $rootScope.selfLink = "{{selfLink}}";
                $rootScope.containerName = "{{containerName}}";
                $rootScope.accessToken = "";
                $rootScope.preventSocket = true;
                $rootScope.rows = 45;
                $rootScope.cols = 168;
            })

            /* Our custom WebSocket factory adapts the url */
            .factory("CustomWebSockets", function($rootScope) {
                return function CustomWebSocket(url, protocols) {
                    url = $rootScope.baseUrl + url;
                    if ($rootScope.accessToken)
                        url += "&access_token=" + $rootScope.accessToken;
                    return new WebSocket(url, protocols);
                };
            });
            window.onload = function() {
                $("#vmConsole").click();
            }
    </script>

</body>
</html>
