{"name": "kubernetes-container-terminal", "version": "3.0.1", "description": "Provides a terminal for a kubernetes container in a pod.", "moduleType": ["globals"], "keywords": ["kubernetes", "<PERSON><PERSON>s", "termjs"], "authors": ["<PERSON><PERSON> <PERSON> <<EMAIL>>"], "ignore": ["**/.*", "Gruntfile.js", "*.html", "package.json", "/container-terminal.*", "node_modules", "bower_components", "scratch"], "main": ["dist/xterm.js", "dist/container-terminal.js", "dist/xterm.css", "dist/container-terminal.css"], "dependencies": {"angular": ">=1.3.8 <1.6", "font-awesome": "*"}, "devDependencies": {"patternfly": ">=1.2.1"}}