kubernetes-container-terminal,
kubernetes-container-terminal .terminal-wrapper {
  display: inline-block;
  position: relative;
}

kubernetes-container-terminal .terminal {
  display: inline-block;
  outline: medium none;
  padding: 2px 0 2px 2px;
}

kubernetes-container-terminal .terminal .xterm-viewport::-webkit-scrollbar {
  height: 10px;
  overflow: visible;
  width: 15px;
}

kubernetes-container-terminal .terminal .xterm-viewport::-webkit-scrollbar-corner {
  background: transparent;
}

kubernetes-container-terminal .terminal .xterm-viewport::-webkit-scrollbar-thumb {
  background-color: rgba(255,255,255,.25);
  box-shadow: inset 1px 1px 0 rgba(255,255,255,.1),inset 0 -1px 0 rgba(255,255,255,.07);
}

kubernetes-container-terminal .terminal .xterm-viewport::-webkit-scrollbar-thumb:active,
kubernetes-container-terminal .terminal .xterm-viewport::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255,255,255,.35);
}

kubernetes-container-terminal .terminal .xterm-viewport::-webkit-scrollbar-track {
  background: transparent;
}

kubernetes-container-terminal .terminal-actions {
  display: inline-block;
  position: absolute;
  right: 34px;
  top: 10px;
  vertical-align: top;
  z-index: 10;
}