{"name": "kubernetes-container-terminal", "description": "Provides a terminal for a kubernetes container in a pod.", "author": "<PERSON><PERSON> <PERSON>", "version": "3.0.1", "license": "LGPL-2.1-or-later", "main": "dist/container-terminal.js", "devDependencies": {"bower": "*", "grunt": "~0.4.5", "grunt-contrib-connect": "~0.9.0", "grunt-contrib-jshint": "~0.11.1", "grunt-contrib-less": "~1.0.0", "grunt-wiredep": "~2.0.0", "grunt-contrib-watch": "~0.6.1", "grunt-contrib-concat": "~0.5.1", "grunt-contrib-uglify": "*", "grunt-run": "*", "xterm": "3.12.0"}, "dependencies": {"angular": ">=1.3.8 <1.6", "font-awesome": "*"}}