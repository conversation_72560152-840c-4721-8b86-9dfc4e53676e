<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script src="/static/jquery.min.js"></script>
    <script type="text/javascript" src="/static/socket.io.min.js"></script>

    <script type="text/javascript" charset="utf-8">
        $(document).ready(function () {
            var _type = 'top';
            var namespace = "/shell";
            var socket = io.connect('http://' + document.domain + ':' + location.port + namespace);

             socket.emit('client', {'_type': _type});

            socket.on('response', function (res) {
                 console.log(res)
                if (res._type === _type) {
                    var top_info = res.text;
                    document.getElementById("terminal").innerHTML = top_info;
                }

            });

               $(window).bind('beforeunload', function () {    // 离开页面前关闭tail
                    socket.emit('leave', {'_type': _type});

                }
            );
        });
    </script>
    <style type="text/css">

        #terminal {
            background-color: black;
            color: white;
        }

        #terminal div {
        {#width: 1024px;#} text-align: justify;
        }

        table {
        {#width: 1024px;#} table-layout: fixed;
            text-align: right;
        }

        table td, th {
            word-break: keep-all;
            white-space: nowrap;
        }

        table tr td:last-child {
            text-align: left;
        }


    </style>

</head>

<body>


<div>

    <div id="terminal">

    </div>
</div>

</body>


</html>