<html>


<style type="text/css">
body {
    width: 1400px;
    margin: 40px auto;
    font-family: 'trebuchet MS', 'Lucida sans', <PERSON><PERSON>;
    font-size: 14px;
    color: #444;
}

table {
    *border-collapse: collapse; /* IE7 and lower */
    border-spacing: 0;
    width: 100%;

}

.bordered {
    border: solid #ccc 1px;
    -moz-border-radius: 6px;
    -webkit-border-radius: 6px;
    border-radius: 6px;
    -webkit-box-shadow: 0 1px 1px #ccc; 
    -moz-box-shadow: 0 1px 1px #ccc; 
    box-shadow: 0 1px 1px #ccc;         
}

.bordered tr:hover {
    background: #fbf8e9;
    -o-transition: all 0.1s ease-in-out;
    -webkit-transition: all 0.1s ease-in-out;
    -moz-transition: all 0.1s ease-in-out;
    -ms-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;     
}    
    
.bordered td, .bordered th {
    border-left: 1px solid #ccc;
    border-top: 1px solid #ccc;
    padding: 10px;
    text-align: left;    
}

.bordered th {
    background-color: #dce9f9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#ebf3fc), to(#dce9f9));
    background-image: -webkit-linear-gradient(top, #ebf3fc, #dce9f9);
    background-image:    -moz-linear-gradient(top, #ebf3fc, #dce9f9);
    background-image:     -ms-linear-gradient(top, #ebf3fc, #dce9f9);
    background-image:      -o-linear-gradient(top, #ebf3fc, #dce9f9);
    background-image:         linear-gradient(top, #ebf3fc, #dce9f9);
    -webkit-box-shadow: 0 1px 0 rgba(255,255,255,.8) inset; 
    -moz-box-shadow:0 1px 0 rgba(255,255,255,.8) inset;  
    box-shadow: 0 1px 0 rgba(255,255,255,.8) inset;        
    border-top: none;
    text-shadow: 0 1px 0 rgba(255,255,255,.5); 
}

.bordered td:first-child, .bordered th:first-child {
    border-left: none;
}

.bordered th:first-child {
    -moz-border-radius: 6px 0 0 0;
    -webkit-border-radius: 6px 0 0 0;
    border-radius: 6px 0 0 0;
}

.bordered th:last-child {
    -moz-border-radius: 0 6px 0 0;
    -webkit-border-radius: 0 6px 0 0;
    border-radius: 0 6px 0 0;
}

.bordered th:only-child{
    -moz-border-radius: 6px 6px 0 0;
    -webkit-border-radius: 6px 6px 0 0;
    border-radius: 6px 6px 0 0;
}

.bordered tr:last-child td:first-child {
    -moz-border-radius: 0 0 0 6px;
    -webkit-border-radius: 0 0 0 6px;
    border-radius: 0 0 0 6px;
}

.bordered tr:last-child td:last-child {
    -moz-border-radius: 0 0 6px 0;
    -webkit-border-radius: 0 0 6px 0;
    border-radius: 0 0 6px 0;
}
</style>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Web Log Parser</title>
</head>



  <body>
  <div align="center">
    <h1 align="center"><a href="https://github.com/JeffXue/web-log-parser" target="github">Web Log Parser</a></h1>
    <table class="bordered">
      <caption align="left">Overall Analyzed Requests</caption>
      <tr>
        <th><strong>日志文件</strong></th>
        <td colspan="3"><strong>invoke-time.log</strong></td>
        <td>
          <a href="#" target="_goaccess">GoAccess报告已设置为无效，无法查看</a>
        </td>
      </tr>
      <tr>
        <th><strong>总PV</strong></th>
        <th><strong>总IP</strong></th>
        <th><strong>每秒处理请求数（峰值）</strong></th>
        <th><strong>峰值时间点</strong></th>
        <th><strong>每秒处理请求数（均值）</strong></th>
      </tr>
      <tr>
        <td><strong>1478</strong></td>
        <td><strong>1</strong></td>
        <td><strong>2</strong></td>
        <td><strong>2024-10-09 14:20:02,406</strong></td>
        <td><strong>1</strong></td>
      </tr>
    </table>

    <br>

    
      <br>
      <table class="bordered">
        <tr>
          
            <td>
              <div id="costPChart" style="height:400px; width: 670px;"></div>
            </td>
            <td>
              <div id="methodChart" style="height:400px; width: 670px;"></div>
            </td>
          
        </tr>
      </table>
    

    <table class="bordered">
      <tr>
        <td>
          <div id="hoursChart" style="height:400px; width: 670px;"></div>
        </td>
        <td>
          <div id="minutesChart" style="height:400px; width: 670px;"></div>
        </td>
      </tr>
    </table>

    


    

    <br>

    
      <table class="bordered">
        <caption>
          Top requests
        </caption>
        
          <tr>
            <th colspan="6" rowspan="2"><strong>Requests</strong></th>
            <th rowspan="2"><strong>访问量</strong></th>
            <th rowspan="2"><strong>比例</strong></th>
            <th rowspan="2"><strong>每秒处理<br/>请求数（峰值）</strong></th>
            <th rowspan="2"><strong>Method</strong></th>
            <th colspan="4"><strong>耗时(秒)</strong></th>
          </tr>
          <tr>
            <th><strong>均值</strong></th>
            <th><strong>90%</strong></th>
            <th><strong>中值</strong></th>
            <th><strong>方差</strong></th>
          </tr>
        

        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/datastores</strong>
          </td>
          <td><strong>872</strong></td>
          <td><strong>58.999%</strong></td>
          <td><strong>2</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>83.295</strong></td>
          <td><strong>47.000</strong></td>
          <td><strong>34.000</strong></td>
          <td><strong>966405</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/thrid-stor/licenses</strong>
          </td>
          <td><strong>203</strong></td>
          <td><strong>13.735%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>4.429</strong></td>
          <td><strong>9.800</strong></td>
          <td><strong>3.000</strong></td>
          <td><strong>11</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/virtualVolume</strong>
          </td>
          <td><strong>103</strong></td>
          <td><strong>6.969%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>13.320</strong></td>
          <td><strong>15.800</strong></td>
          <td><strong>8.000</strong></td>
          <td><strong>1053</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/virtualVolume/import</strong>
          </td>
          <td><strong>95</strong></td>
          <td><strong>6.428%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>72.358</strong></td>
          <td><strong>79.200</strong></td>
          <td><strong>60.000</strong></td>
          <td><strong>13639</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/datastores/4vm</strong>
          </td>
          <td><strong>49</strong></td>
          <td><strong>3.315%</strong></td>
          <td><strong>2</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>7.122</strong></td>
          <td><strong>15.000</strong></td>
          <td><strong>4.000</strong></td>
          <td><strong>34</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/icinder/v1/resources/statistics-capacity</strong>
          </td>
          <td><strong>48</strong></td>
          <td><strong>3.248%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>193.417</strong></td>
          <td><strong>367.400</strong></td>
          <td><strong>40.500</strong></td>
          <td><strong>52870</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/datastores/event/notify</strong>
          </td>
          <td><strong>33</strong></td>
          <td><strong>2.233%</strong></td>
          <td><strong>2</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>265.879</strong></td>
          <td><strong>619.600</strong></td>
          <td><strong>214.000</strong></td>
          <td><strong>60375</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/imanila/v2/types/import</strong>
          </td>
          <td><strong>26</strong></td>
          <td><strong>1.759%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>17.923</strong></td>
          <td><strong>22.500</strong></td>
          <td><strong>15.500</strong></td>
          <td><strong>75</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/imanila/v2/platform-quota/2</strong>
          </td>
          <td><strong>11</strong></td>
          <td><strong>0.744%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>28.909</strong></td>
          <td><strong>37.000</strong></td>
          <td><strong>28.000</strong></td>
          <td><strong>27</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/imanila/v2/platform-quota/1</strong>
          </td>
          <td><strong>8</strong></td>
          <td><strong>0.541%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>30.000</strong></td>
          <td><strong>37.600</strong></td>
          <td><strong>28.000</strong></td>
          <td><strong>47</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/imanila/v2/pools</strong>
          </td>
          <td><strong>6</strong></td>
          <td><strong>0.406%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>9.667</strong></td>
          <td><strong>20.000</strong></td>
          <td><strong>7.000</strong></td>
          <td><strong>85</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/datastores/56f6f8f9-68ca-4f86-85f8-d5407cef11a9/host</strong>
          </td>
          <td><strong>4</strong></td>
          <td><strong>0.271%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>80.250</strong></td>
          <td><strong>101.600</strong></td>
          <td><strong>74.500</strong></td>
          <td><strong>349</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/datastores/e6fe0cff-8d60-4b26-bc78-be4b4e5836a7</strong>
          </td>
          <td><strong>4</strong></td>
          <td><strong>0.271%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>65.000</strong></td>
          <td><strong>68.000</strong></td>
          <td><strong>65.000</strong></td>
          <td><strong>9</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/thrid-stor</strong>
          </td>
          <td><strong>4</strong></td>
          <td><strong>0.271%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>2.250</strong></td>
          <td><strong>2.700</strong></td>
          <td><strong>2.000</strong></td>
          <td><strong>0</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/objstorObj/policy</strong>
          </td>
          <td><strong>3</strong></td>
          <td><strong>0.203%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>5.667</strong></td>
          <td><strong>10.800</strong></td>
          <td><strong>2.000</strong></td>
          <td><strong>26</strong></td>
          </tr>
          
        
          
            
              <tr>
            
          <td colspan="6">
            <strong>/v1/datastores/sync</strong>
          </td>
          <td><strong>2</strong></td>
          <td><strong>0.135%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>1.500</strong></td>
          <td><strong>1.900</strong></td>
          <td><strong>1.500</strong></td>
          <td><strong>0</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/icinder/v1/platform-quota/statisitcs/2</strong>
          </td>
          <td><strong>2</strong></td>
          <td><strong>0.135%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>81.000</strong></td>
          <td><strong>92.200</strong></td>
          <td><strong>81.000</strong></td>
          <td><strong>196</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/objstor/accounts</strong>
          </td>
          <td><strong>2</strong></td>
          <td><strong>0.135%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>25.000</strong></td>
          <td><strong>43.400</strong></td>
          <td><strong>25.000</strong></td>
          <td><strong>529</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/virtualVolume/remove</strong>
          </td>
          <td><strong>2</strong></td>
          <td><strong>0.135%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>44.500</strong></td>
          <td><strong>68.900</strong></td>
          <td><strong>44.500</strong></td>
          <td><strong>930</strong></td>
          </tr>
          
        
          
            
              <tr style="color: red">
                
          <td colspan="6">
            <strong>/v1/objectstor/buckets/list</strong>
          </td>
          <td><strong>1</strong></td>
          <td><strong>0.068%</strong></td>
          <td><strong>1</strong></td>
          <td><strong>GET</strong></td>
          <td><strong>39.000</strong></td>
          <td><strong>39.000</strong></td>
          <td><strong>39.000</strong></td>
          <td><strong>0</strong></td>
          </tr>
          
        

      </table>
    
  </div>

  <script src="echarts.js"></script>

  <script type="text/javascript">
    var ec = echarts;
          var hoursChart = ec.init(document.getElementById('hoursChart'));

          var hoursOption = {
            title: {
              text: '每小时处理请求数量',
              subtext: ''
            },
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['处理请求数']
            },
            toolbox: {
              show: true,
              feature: {
                mark: {show: false},
                dataView: {show: false, readOnly: false},
                magicType: {show: true, type: ['line', 'bar']},
                restore: {show: false},
                saveAsImage: {show: true}
              }
            },
            calculable: true,
            xAxis: [
              {
                type: 'category',
                boundaryGap: false,
                data: ['2024-10-09 00:00:00','2024-10-09 00:05:00','2024-10-09 00:10:00','2024-10-09 00:15:00','2024-10-09 00:20:00','2024-10-09 00:25:00','2024-10-09 00:30:00','2024-10-09 00:35:00','2024-10-09 00:40:00','2024-10-09 00:45:00','2024-10-09 00:50:00','2024-10-09 00:54:00','2024-10-09 00:55:00','2024-10-09 01:00:00','2024-10-09 01:05:00','2024-10-09 01:10:00','2024-10-09 01:15:00','2024-10-09 01:20:00','2024-10-09 01:25:00','2024-10-09 01:30:00','2024-10-09 01:35:00','2024-10-09 01:40:00','2024-10-09 01:45:00','2024-10-09 01:50:00','2024-10-09 01:54:00','2024-10-09 01:55:00','2024-10-09 02:00:00','2024-10-09 02:05:00','2024-10-09 02:10:00','2024-10-09 02:15:00','2024-10-09 02:20:00','2024-10-09 02:25:00','2024-10-09 02:30:00','2024-10-09 02:35:00','2024-10-09 02:40:00','2024-10-09 02:45:00','2024-10-09 02:50:00','2024-10-09 02:55:00','2024-10-09 03:00:00','2024-10-09 03:05:00','2024-10-09 03:10:00','2024-10-09 03:15:00','2024-10-09 03:20:00','2024-10-09 03:25:00','2024-10-09 03:30:00','2024-10-09 03:35:00','2024-10-09 03:40:00','2024-10-09 03:45:00','2024-10-09 03:50:00','2024-10-09 03:55:00','2024-10-09 04:00:00','2024-10-09 04:05:00','2024-10-09 04:10:00','2024-10-09 04:15:00','2024-10-09 04:20:00','2024-10-09 04:25:00','2024-10-09 04:30:00','2024-10-09 04:35:00','2024-10-09 04:40:00','2024-10-09 04:45:00','2024-10-09 04:50:00','2024-10-09 04:55:00','2024-10-09 05:00:00','2024-10-09 05:05:00','2024-10-09 05:10:00','2024-10-09 05:15:00','2024-10-09 05:20:00','2024-10-09 05:25:00','2024-10-09 05:30:00','2024-10-09 05:35:00','2024-10-09 05:40:00','2024-10-09 05:45:00','2024-10-09 05:50:00','2024-10-09 05:55:00','2024-10-09 06:00:00','2024-10-09 06:05:00','2024-10-09 06:10:00','2024-10-09 06:15:00','2024-10-09 06:20:00','2024-10-09 06:25:00','2024-10-09 06:30:00','2024-10-09 06:35:00','2024-10-09 06:40:00','2024-10-09 06:45:00','2024-10-09 06:50:00','2024-10-09 06:55:00','2024-10-09 07:00:00','2024-10-09 07:05:00','2024-10-09 07:10:00','2024-10-09 07:15:00','2024-10-09 07:20:00','2024-10-09 07:25:00','2024-10-09 07:30:00','2024-10-09 07:35:00','2024-10-09 07:40:00','2024-10-09 07:45:00','2024-10-09 07:50:00','2024-10-09 07:55:00','2024-10-09 08:00:00','2024-10-09 08:05:00','2024-10-09 08:10:00','2024-10-09 08:15:00','2024-10-09 08:20:00','2024-10-09 08:25:00','2024-10-09 08:28:00','2024-10-09 08:30:00','2024-10-09 08:35:00','2024-10-09 08:38:00','2024-10-09 08:39:00','2024-10-09 08:40:00','2024-10-09 08:41:00','2024-10-09 08:45:00','2024-10-09 08:50:00','2024-10-09 08:55:00','2024-10-09 09:00:00','2024-10-09 09:05:00','2024-10-09 09:40:00','2024-10-09 09:45:00','2024-10-09 09:50:00','2024-10-09 09:55:00','2024-10-09 10:00:00','2024-10-09 10:05:00','2024-10-09 10:10:00','2024-10-09 10:15:00','2024-10-09 10:20:00','2024-10-09 10:25:00','2024-10-09 10:30:00','2024-10-09 10:35:00','2024-10-09 10:40:00','2024-10-09 10:45:00','2024-10-09 10:50:00','2024-10-09 10:53:00','2024-10-09 10:54:00','2024-10-09 10:55:00','2024-10-09 10:56:00','2024-10-09 10:57:00','2024-10-09 10:58:00','2024-10-09 10:59:00','2024-10-09 11:00:00','2024-10-09 11:01:00','2024-10-09 11:02:00','2024-10-09 11:03:00','2024-10-09 11:04:00','2024-10-09 11:05:00','2024-10-09 11:06:00','2024-10-09 11:07:00','2024-10-09 11:08:00','2024-10-09 11:09:00','2024-10-09 11:10:00','2024-10-09 11:11:00','2024-10-09 11:12:00','2024-10-09 11:13:00','2024-10-09 11:14:00','2024-10-09 11:15:00','2024-10-09 11:16:00','2024-10-09 11:17:00','2024-10-09 11:18:00','2024-10-09 11:19:00','2024-10-09 11:20:00','2024-10-09 11:21:00','2024-10-09 11:22:00','2024-10-09 11:23:00','2024-10-09 11:24:00','2024-10-09 11:25:00','2024-10-09 11:26:00','2024-10-09 11:27:00','2024-10-09 11:28:00','2024-10-09 11:29:00','2024-10-09 11:30:00','2024-10-09 11:31:00','2024-10-09 11:32:00','2024-10-09 11:33:00','2024-10-09 11:34:00','2024-10-09 11:35:00','2024-10-09 11:36:00','2024-10-09 11:37:00','2024-10-09 11:38:00','2024-10-09 11:39:00','2024-10-09 11:40:00','2024-10-09 11:41:00','2024-10-09 11:42:00','2024-10-09 11:43:00','2024-10-09 11:44:00','2024-10-09 11:45:00','2024-10-09 11:46:00','2024-10-09 11:47:00','2024-10-09 11:48:00','2024-10-09 11:49:00','2024-10-09 11:50:00','2024-10-09 11:51:00','2024-10-09 11:52:00','2024-10-09 11:53:00','2024-10-09 11:54:00','2024-10-09 11:55:00','2024-10-09 11:56:00','2024-10-09 11:57:00','2024-10-09 11:58:00','2024-10-09 11:59:00','2024-10-09 12:00:00','2024-10-09 12:01:00','2024-10-09 12:02:00','2024-10-09 12:03:00','2024-10-09 12:04:00','2024-10-09 12:05:00','2024-10-09 12:06:00','2024-10-09 12:07:00','2024-10-09 12:08:00','2024-10-09 12:09:00','2024-10-09 12:10:00','2024-10-09 12:11:00','2024-10-09 12:12:00','2024-10-09 12:13:00','2024-10-09 12:14:00','2024-10-09 12:15:00','2024-10-09 12:16:00','2024-10-09 12:17:00','2024-10-09 12:18:00','2024-10-09 12:19:00','2024-10-09 12:20:00','2024-10-09 12:21:00','2024-10-09 12:22:00','2024-10-09 12:23:00','2024-10-09 12:24:00','2024-10-09 12:25:00','2024-10-09 12:26:00','2024-10-09 12:27:00','2024-10-09 12:28:00','2024-10-09 12:29:00','2024-10-09 12:30:00','2024-10-09 12:31:00','2024-10-09 12:32:00','2024-10-09 12:33:00','2024-10-09 12:34:00','2024-10-09 12:35:00','2024-10-09 12:36:00','2024-10-09 12:37:00','2024-10-09 12:38:00','2024-10-09 12:39:00','2024-10-09 12:40:00','2024-10-09 12:41:00','2024-10-09 12:42:00','2024-10-09 12:43:00','2024-10-09 12:44:00','2024-10-09 12:45:00','2024-10-09 12:46:00','2024-10-09 12:47:00','2024-10-09 12:48:00','2024-10-09 12:49:00','2024-10-09 12:50:00','2024-10-09 12:51:00','2024-10-09 12:52:00','2024-10-09 12:53:00','2024-10-09 12:54:00','2024-10-09 12:55:00','2024-10-09 12:56:00','2024-10-09 12:57:00','2024-10-09 12:58:00','2024-10-09 12:59:00','2024-10-09 13:00:00','2024-10-09 13:01:00','2024-10-09 13:02:00','2024-10-09 13:03:00','2024-10-09 13:04:00','2024-10-09 13:05:00','2024-10-09 13:06:00','2024-10-09 13:07:00','2024-10-09 13:08:00','2024-10-09 13:09:00','2024-10-09 13:10:00','2024-10-09 13:11:00','2024-10-09 13:12:00','2024-10-09 13:13:00','2024-10-09 13:14:00','2024-10-09 13:15:00','2024-10-09 13:16:00','2024-10-09 13:17:00','2024-10-09 13:18:00','2024-10-09 13:19:00','2024-10-09 13:20:00','2024-10-09 13:21:00','2024-10-09 13:22:00','2024-10-09 13:23:00','2024-10-09 13:24:00','2024-10-09 13:25:00','2024-10-09 13:26:00','2024-10-09 13:27:00','2024-10-09 13:28:00','2024-10-09 13:29:00','2024-10-09 13:30:00','2024-10-09 13:31:00','2024-10-09 13:32:00','2024-10-09 13:33:00','2024-10-09 13:34:00','2024-10-09 13:35:00','2024-10-09 13:36:00','2024-10-09 13:37:00','2024-10-09 13:38:00','2024-10-09 13:39:00','2024-10-09 13:40:00','2024-10-09 13:41:00','2024-10-09 13:42:00','2024-10-09 13:43:00','2024-10-09 13:44:00','2024-10-09 13:45:00','2024-10-09 13:46:00','2024-10-09 13:47:00','2024-10-09 13:48:00','2024-10-09 13:49:00','2024-10-09 13:50:00','2024-10-09 13:51:00','2024-10-09 13:52:00','2024-10-09 13:53:00','2024-10-09 13:54:00','2024-10-09 13:55:00','2024-10-09 13:56:00','2024-10-09 13:57:00','2024-10-09 13:58:00','2024-10-09 13:59:00','2024-10-09 14:00:00','2024-10-09 14:01:00','2024-10-09 14:02:00','2024-10-09 14:03:00','2024-10-09 14:04:00','2024-10-09 14:05:00','2024-10-09 14:06:00','2024-10-09 14:07:00','2024-10-09 14:08:00','2024-10-09 14:09:00','2024-10-09 14:10:00','2024-10-09 14:11:00','2024-10-09 14:12:00','2024-10-09 14:13:00','2024-10-09 14:14:00','2024-10-09 14:15:00','2024-10-09 14:16:00','2024-10-09 14:17:00','2024-10-09 14:18:00','2024-10-09 14:19:00','2024-10-09 14:20:00','2024-10-09 14:21:00','2024-10-09 14:22:00','2024-10-09 14:23:00','2024-10-09 14:24:00','2024-10-09 14:25:00','2024-10-09 14:26:00','2024-10-09 14:27:00','2024-10-09 14:28:00','2024-10-09 14:29:00','2024-10-09 14:30:00','2024-10-09 14:31:00','2024-10-09 14:32:00','2024-10-09 14:33:00','2024-10-09 14:34:00','2024-10-09 14:35:00','2024-10-09 14:36:00','2024-10-09 14:37:00','2024-10-09 14:38:00','2024-10-09 14:39:00','2024-10-09 14:40:00','2024-10-09 14:41:00','2024-10-09 14:42:00','2024-10-09 14:43:00','2024-10-09 14:44:00','2024-10-09 14:45:00','2024-10-09 14:46:00','2024-10-09 14:47:00','2024-10-09 14:48:00','2024-10-09 14:49:00','2024-10-09 14:50:00','2024-10-09 14:51:00','2024-10-09 14:52:00','2024-10-09 14:53:00','2024-10-09 14:54:00','2024-10-09 14:55:00','2024-10-09 14:56:00','2024-10-09 14:57:00','2024-10-09 14:58:00','2024-10-09 14:59:00','2024-10-09 15:00:00','2024-10-09 15:01:00','2024-10-09 15:02:00','2024-10-09 15:03:00','2024-10-09 15:04:00','2024-10-09 15:05:00','2024-10-09 15:06:00','2024-10-09 15:07:00','2024-10-09 15:08:00','2024-10-09 15:09:00','2024-10-09 15:10:00','2024-10-09 15:11:00','2024-10-09 15:12:00','2024-10-09 15:13:00','2024-10-09 15:14:00','2024-10-09 15:15:00','2024-10-09 15:16:00','2024-10-09 15:17:00','2024-10-09 15:18:00','2024-10-09 15:19:00','2024-10-09 15:20:00','2024-10-09 15:21:00','2024-10-09 15:22:00','2024-10-09 15:23:00','2024-10-09 15:24:00','2024-10-09 15:25:00','2024-10-09 15:26:00','2024-10-09 15:27:00','2024-10-09 15:28:00','2024-10-09 15:29:00','2024-10-09 15:30:00','2024-10-09 15:31:00','2024-10-09 15:32:00','2024-10-09 15:33:00','2024-10-09 15:34:00','2024-10-09 15:35:00','2024-10-09 15:36:00','2024-10-09 15:37:00','2024-10-09 15:38:00','2024-10-09 15:39:00','2024-10-09 15:40:00','2024-10-09 15:41:00','2024-10-09 15:42:00','2024-10-09 15:43:00','2024-10-09 15:44:00','2024-10-09 15:45:00','2024-10-09 15:46:00','2024-10-09 15:47:00','2024-10-09 15:48:00','2024-10-09 15:49:00','2024-10-09 15:50:00','2024-10-09 15:51:00','2024-10-09 15:52:00','2024-10-09 15:53:00','2024-10-09 15:54:00','2024-10-09 15:55:00','2024-10-09 15:56:00','2024-10-09 15:57:00','2024-10-09 15:58:00','2024-10-09 15:59:00','2024-10-09 16:00:00','2024-10-09 16:01:00','2024-10-09 16:02:00','2024-10-09 16:03:00','2024-10-09 16:04:00','2024-10-09 16:05:00','2024-10-09 16:06:00','2024-10-09 16:07:00','2024-10-09 16:08:00','2024-10-09 16:09:00','2024-10-09 16:10:00','2024-10-09 16:11:00','2024-10-09 16:12:00','2024-10-09 16:13:00','2024-10-09 16:14:00','2024-10-09 16:15:00','2024-10-09 16:16:00','2024-10-09 16:17:00','2024-10-09 16:18:00','2024-10-09 16:19:00','2024-10-09 16:20:00','2024-10-09 16:21:00','2024-10-09 16:22:00','2024-10-09 16:23:00','2024-10-09 16:24:00','2024-10-09 16:25:00','2024-10-09 16:26:00','2024-10-09 16:27:00','2024-10-09 16:28:00','2024-10-09 16:29:00','2024-10-09 16:30:00','2024-10-09 16:31:00','2024-10-09 16:32:00','2024-10-09 16:33:00','2024-10-09 16:34:00','2024-10-09 16:35:00','2024-10-09 16:36:00','2024-10-09 16:37:00','2024-10-09 16:38:00','2024-10-09 16:39:00','2024-10-09 16:40:00','2024-10-09 16:41:00','2024-10-09 16:42:00','2024-10-09 16:43:00','2024-10-09 16:44:00','2024-10-09 16:45:00','2024-10-09 16:46:00','2024-10-09 16:47:00','2024-10-09 16:48:00','2024-10-09 16:49:00','2024-10-09 16:50:00','2024-10-09 16:51:00','2024-10-09 16:52:00','2024-10-09 16:53:00','2024-10-09 16:54:00','2024-10-09 16:55:00','2024-10-09 16:56:00','2024-10-09 16:57:00','2024-10-09 16:58:00','2024-10-09 16:59:00','2024-10-09 17:00:00','2024-10-09 17:01:00','2024-10-09 17:02:00','2024-10-09 17:03:00','2024-10-09 17:04:00','2024-10-09 17:05:00','2024-10-09 17:06:00','2024-10-09 17:07:00','2024-10-09 17:08:00','2024-10-09 17:09:00','2024-10-09 17:10:00','2024-10-09 17:11:00','2024-10-09 17:12:00','2024-10-09 17:13:00','2024-10-09 17:14:00','2024-10-09 17:15:00','2024-10-09 17:16:00','2024-10-09 17:17:00','2024-10-09 17:18:00','2024-10-09 17:19:00','2024-10-09 17:20:00','2024-10-09 17:21:00',]
              }
            ],
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  formatter: '{value}'
                }
              }
            ],
            series: [
              {
                name: '处理请求数',
                type: 'line',
                data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 3, 4, 1, 1, 10, 2, 1, 2, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 4, 3, 2, 6, 1, 3, 1, 25, 1, 1, 2, 1, 3, 1, 1, 5, 2, 2, 1, 1, 5, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 5, 1, 1, 1, 4, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 3, 1, 1, 1, 1, 2, 1, 1, 1, 1, 3, 7, 1, 1, 1, 7, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 3, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 5, 1, 1, 1, 22, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 3, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 5, 1, 1, 1, 3, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 3, 1, 1, 1, 1, 2, 2, 1, 1, 2, 2, 1, 1, 1, 1, 4, 2, 1, 1, 1, 4, 2, 6, 11, 6, 2, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 4, 1, 1, 6, 2, 5, 3, 2, 1, 3, 1, 9, 1, 1, 3, 1, 1, 1, 1, 2, 1, 1, 1, 2, 3, 1, 1, 1, 2, 23, 6, 1, 2, 2, 11, 7, 2, 7, 1, 3, 1, 1, 2, 1, 9, 23, 23, 24, 26, 23, 23, 23, 26, 23, 23, 23, 21, 20, 25, 22, 21, 21, 35, 41, 18, 5, 1, 3, 2, 4, 1, 1, 1, 2, 2, 1, 2, 1, 1, 2, 2, 1, 1, 1, 3, 1, 1, 1, 1, 3, 1, 2, 1, 1, 2, 3, 4, 9, 1, 32, 1, 2, 7, 3, 2, 1, 3, 1, 1, 2, 1, 1, 3, 1, 3, 1, 4, 4, 7, 2, 1, 1, 1, 1, 2, 3, 14, 14, 7, 4, 1, 3, 3, 3, 3, 1, 1, 1, 1, 5, 1, 2, 3, 2, 3, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 4, 3, 1, 1, 3, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 1, 2, 1, 3, 1, 1, 1, 1, 2, 8, 2, 3, 2, 2, 3, 3, 2, 2, 3, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 3, 5, 4, 1, 1, 2, 1, ],
                markPoint: {
                  data: [
                    {type: 'max', name: '最大值'},
                    {type: 'min', name: '最小值'}
                  ]
                },
                markLine: {
                  data: [
                    {type: 'average', name: '平均值'}
                  ]
                }
              }
            ]
          };
          hoursChart.setOption(hoursOption);

          var minuteChart = ec.init(document.getElementById('minutesChart'));

          var minuteOption = {
            title: {
              text: '每分钟处理请求数量',
              subtext: ''
            },
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['处理请求数']
            },
            toolbox: {
              show: true,
              feature: {
                mark: {show: false},
                dataView: {show: false, readOnly: false},
                magicType: {show: true, type: ['line', 'bar']},
                restore: {show: false},
                saveAsImage: {show: true}
              }
            },
            calculable: true,
            xAxis: [
              {
                type: 'category',
                boundaryGap: false,
                data: ['2024-10-09 00:00:00,047','2024-10-09 00:05:00,039','2024-10-09 00:10:00,033','2024-10-09 00:15:00,027','2024-10-09 00:20:00,032','2024-10-09 00:25:00,022','2024-10-09 00:30:00,018','2024-10-09 00:35:00,013','2024-10-09 00:40:00,023','2024-10-09 00:45:00,018','2024-10-09 00:50:00,034','2024-10-09 00:54:43,638','2024-10-09 00:54:43,758','2024-10-09 00:55:00,014','2024-10-09 01:00:00,043','2024-10-09 01:05:00,014','2024-10-09 01:10:00,023','2024-10-09 01:15:00,032','2024-10-09 01:20:00,047','2024-10-09 01:25:00,026','2024-10-09 01:30:00,039','2024-10-09 01:35:00,030','2024-10-09 01:40:00,025','2024-10-09 01:45:00,025','2024-10-09 01:50:00,032','2024-10-09 01:54:52,647','2024-10-09 01:54:52,797','2024-10-09 01:55:00,017','2024-10-09 02:00:00,037','2024-10-09 02:05:00,013','2024-10-09 02:10:00,026','2024-10-09 02:15:00,048','2024-10-09 02:20:00,024','2024-10-09 02:25:00,020','2024-10-09 02:30:00,034','2024-10-09 02:35:00,015','2024-10-09 02:40:00,033','2024-10-09 02:45:00,029','2024-10-09 02:50:00,035','2024-10-09 02:55:00,032','2024-10-09 03:00:00,045','2024-10-09 03:05:00,018','2024-10-09 03:10:00,023','2024-10-09 03:15:00,023','2024-10-09 03:20:00,028','2024-10-09 03:25:00,013','2024-10-09 03:25:03,078','2024-10-09 03:25:03,199','2024-10-09 03:30:00,029','2024-10-09 03:35:00,028','2024-10-09 03:40:00,023','2024-10-09 03:45:00,014','2024-10-09 03:50:00,032','2024-10-09 03:55:00,012','2024-10-09 04:00:00,052','2024-10-09 04:05:00,023','2024-10-09 04:10:00,018','2024-10-09 04:15:00,021','2024-10-09 04:20:00,023','2024-10-09 04:25:00,014','2024-10-09 04:30:00,016','2024-10-09 04:35:00,014','2024-10-09 04:40:00,024','2024-10-09 04:45:00,030','2024-10-09 04:50:00,029','2024-10-09 04:55:00,014','2024-10-09 04:55:19,118','2024-10-09 04:55:19,244','2024-10-09 05:00:00,040','2024-10-09 05:05:00,019','2024-10-09 05:10:00,031','2024-10-09 05:15:00,031','2024-10-09 05:20:00,030','2024-10-09 05:25:00,032','2024-10-09 05:25:21,102','2024-10-09 05:25:21,228','2024-10-09 05:30:00,035','2024-10-09 05:35:00,014','2024-10-09 05:40:00,025','2024-10-09 05:45:00,027','2024-10-09 05:50:00,030','2024-10-09 05:55:00,032','2024-10-09 05:55:27,954','2024-10-09 05:55:28,083','2024-10-09 06:00:00,036','2024-10-09 06:05:00,033','2024-10-09 06:10:00,029','2024-10-09 06:15:00,012','2024-10-09 06:20:00,024','2024-10-09 06:25:00,038','2024-10-09 06:25:31,905','2024-10-09 06:25:32,040','2024-10-09 06:30:00,023','2024-10-09 06:35:00,015','2024-10-09 06:40:00,018','2024-10-09 06:45:00,039','2024-10-09 06:50:00,032','2024-10-09 06:55:00,029','2024-10-09 06:55:37,030','2024-10-09 06:55:37,167','2024-10-09 07:00:00,039','2024-10-09 07:05:00,012','2024-10-09 07:10:00,029','2024-10-09 07:15:00,028','2024-10-09 07:20:00,033','2024-10-09 07:25:00,020','2024-10-09 07:25:42,304','2024-10-09 07:25:42,416','2024-10-09 07:30:00,018','2024-10-09 07:35:00,027','2024-10-09 07:40:00,033','2024-10-09 07:45:00,021','2024-10-09 07:50:00,023','2024-10-09 07:55:00,016','2024-10-09 07:55:47,651','2024-10-09 07:55:47,781','2024-10-09 08:00:00,041','2024-10-09 08:05:00,033','2024-10-09 08:10:00,018','2024-10-09 08:15:00,034','2024-10-09 08:20:00,032','2024-10-09 08:25:00,021','2024-10-09 08:25:51,127','2024-10-09 08:25:51,260','2024-10-09 08:28:23,141','2024-10-09 08:28:26,526','2024-10-09 08:28:47,848','2024-10-09 08:28:47,917','2024-10-09 08:30:00,039','2024-10-09 08:35:00,033','2024-10-09 08:38:02,584','2024-10-09 08:38:05,981','2024-10-09 08:38:30,183','2024-10-09 08:38:30,251','2024-10-09 08:38:31,920','2024-10-09 08:38:32,035','2024-10-09 08:38:52,217','2024-10-09 08:38:52,331','2024-10-09 08:38:54,259','2024-10-09 08:38:54,369','2024-10-09 08:39:03,855','2024-10-09 08:39:03,981','2024-10-09 08:40:00,031','2024-10-09 08:41:48,657','2024-10-09 08:41:48,760','2024-10-09 08:45:00,027','2024-10-09 08:50:00,029','2024-10-09 08:55:00,022','2024-10-09 08:55:58,549','2024-10-09 08:55:58,666','2024-10-09 09:00:00,038','2024-10-09 09:05:00,018','2024-10-09 09:40:00,199','2024-10-09 09:45:00,024','2024-10-09 09:50:00,030','2024-10-09 09:55:00,031','2024-10-09 10:00:00,044','2024-10-09 10:05:00,014','2024-10-09 10:10:00,031','2024-10-09 10:15:00,035','2024-10-09 10:20:00,029','2024-10-09 10:25:00,020','2024-10-09 10:30:00,028','2024-10-09 10:35:00,037','2024-10-09 10:40:00,021','2024-10-09 10:45:00,021','2024-10-09 10:50:00,031','2024-10-09 10:53:58,712','2024-10-09 10:54:40,700','2024-10-09 10:54:40,809','2024-10-09 10:54:51,311','2024-10-09 10:54:51,425','2024-10-09 10:54:58,636','2024-10-09 10:55:00,800','2024-10-09 10:55:35,438','2024-10-09 10:55:35,562','2024-10-09 10:55:58,628','2024-10-09 10:56:21,738','2024-10-09 10:56:21,859','2024-10-09 10:56:58,632','2024-10-09 10:57:56,168','2024-10-09 10:57:58,643','2024-10-09 10:58:07,892','2024-10-09 10:58:12,187','2024-10-09 10:58:12,189','2024-10-09 10:58:14,491','2024-10-09 10:58:23,736','2024-10-09 10:58:58,643','2024-10-09 10:59:58,639','2024-10-09 11:00:00,042','2024-10-09 11:00:00,314','2024-10-09 11:00:58,642','2024-10-09 11:01:59,672','2024-10-09 11:02:02,934','2024-10-09 11:02:03,807','2024-10-09 11:02:04,896','2024-10-09 11:02:08,711','2024-10-09 11:02:09,791','2024-10-09 11:02:22,911','2024-10-09 11:02:24,000','2024-10-09 11:02:35,688','2024-10-09 11:02:35,796','2024-10-09 11:02:35,803','2024-10-09 11:02:40,876','2024-10-09 11:02:40,879','2024-10-09 11:02:40,884','2024-10-09 11:02:41,999','2024-10-09 11:02:42,106','2024-10-09 11:02:42,207','2024-10-09 11:02:42,234','2024-10-09 11:02:42,343','2024-10-09 11:02:46,361','2024-10-09 11:02:46,487','2024-10-09 11:02:46,789','2024-10-09 11:02:46,871','2024-10-09 11:02:55,253','2024-10-09 11:02:55,362','2024-10-09 11:02:58,632','2024-10-09 11:03:58,634','2024-10-09 11:04:58,641','2024-10-09 11:05:00,030','2024-10-09 11:05:58,631','2024-10-09 11:06:58,638','2024-10-09 11:07:02,852','2024-10-09 11:07:02,953','2024-10-09 11:07:58,643','2024-10-09 11:08:58,638','2024-10-09 11:09:58,645','2024-10-09 11:10:00,022','2024-10-09 11:10:01,022','2024-10-09 11:10:21,392','2024-10-09 11:10:32,367','2024-10-09 11:10:58,638','2024-10-09 11:11:45,172','2024-10-09 11:11:58,638','2024-10-09 11:12:12,697','2024-10-09 11:12:58,646','2024-10-09 11:13:58,634','2024-10-09 11:14:58,639','2024-10-09 11:15:00,026','2024-10-09 11:15:00,246','2024-10-09 11:15:03,537','2024-10-09 11:15:03,620','2024-10-09 11:15:58,647','2024-10-09 11:16:58,645','2024-10-09 11:17:58,631','2024-10-09 11:18:58,638','2024-10-09 11:19:58,632','2024-10-09 11:20:00,040','2024-10-09 11:20:58,641','2024-10-09 11:21:58,640','2024-10-09 11:22:58,639','2024-10-09 11:23:58,653','2024-10-09 11:24:58,633','2024-10-09 11:25:00,031','2024-10-09 11:25:58,632','2024-10-09 11:26:23,893','2024-10-09 11:26:24,020','2024-10-09 11:26:33,869','2024-10-09 11:26:33,981','2024-10-09 11:26:58,644','2024-10-09 11:27:58,634','2024-10-09 11:28:58,636','2024-10-09 11:29:58,635','2024-10-09 11:30:00,019','2024-10-09 11:30:00,267','2024-10-09 11:30:08,520','2024-10-09 11:30:58,632','2024-10-09 11:31:58,635','2024-10-09 11:32:58,634','2024-10-09 11:33:58,629','2024-10-09 11:34:58,638','2024-10-09 11:35:00,018','2024-10-09 11:35:58,639','2024-10-09 11:36:58,646','2024-10-09 11:37:58,628','2024-10-09 11:38:58,647','2024-10-09 11:39:58,636','2024-10-09 11:40:00,034','2024-10-09 11:40:58,634','2024-10-09 11:41:58,637','2024-10-09 11:42:58,640','2024-10-09 11:43:58,627','2024-10-09 11:44:58,632','2024-10-09 11:45:00,019','2024-10-09 11:45:00,243','2024-10-09 11:45:58,637','2024-10-09 11:46:58,633','2024-10-09 11:47:58,625','2024-10-09 11:48:58,630','2024-10-09 11:49:58,636','2024-10-09 11:50:00,036','2024-10-09 11:50:58,632','2024-10-09 11:51:58,634','2024-10-09 11:52:58,636','2024-10-09 11:53:58,642','2024-10-09 11:54:58,645','2024-10-09 11:55:00,022','2024-10-09 11:55:30,470','2024-10-09 11:55:58,633','2024-10-09 11:56:17,082','2024-10-09 11:56:17,191','2024-10-09 11:56:32,328','2024-10-09 11:56:32,462','2024-10-09 11:56:37,138','2024-10-09 11:56:37,230','2024-10-09 11:56:58,636','2024-10-09 11:57:58,634','2024-10-09 11:58:58,627','2024-10-09 11:59:58,625','2024-10-09 12:00:00,060','2024-10-09 12:00:00,338','2024-10-09 12:00:01,543','2024-10-09 12:00:01,650','2024-10-09 12:00:06,551','2024-10-09 12:00:06,634','2024-10-09 12:00:58,635','2024-10-09 12:01:58,630','2024-10-09 12:02:58,627','2024-10-09 12:03:58,643','2024-10-09 12:04:58,628','2024-10-09 12:05:00,028','2024-10-09 12:05:58,642','2024-10-09 12:06:58,626','2024-10-09 12:07:58,631','2024-10-09 12:08:58,646','2024-10-09 12:09:58,628','2024-10-09 12:10:00,015','2024-10-09 12:10:58,629','2024-10-09 12:11:58,645','2024-10-09 12:12:58,624','2024-10-09 12:13:58,627','2024-10-09 12:14:58,648','2024-10-09 12:15:00,033','2024-10-09 12:15:00,235','2024-10-09 12:15:58,626','2024-10-09 12:16:58,632','2024-10-09 12:17:58,634','2024-10-09 12:18:58,632','2024-10-09 12:19:58,627','2024-10-09 12:20:00,033','2024-10-09 12:20:58,631','2024-10-09 12:21:58,625','2024-10-09 12:22:58,624','2024-10-09 12:23:58,625','2024-10-09 12:24:58,632','2024-10-09 12:25:00,026','2024-10-09 12:25:58,634','2024-10-09 12:26:37,057','2024-10-09 12:26:37,182','2024-10-09 12:26:37,212','2024-10-09 12:26:37,317','2024-10-09 12:26:58,628','2024-10-09 12:27:58,634','2024-10-09 12:28:58,625','2024-10-09 12:29:58,628','2024-10-09 12:30:00,019','2024-10-09 12:30:00,264','2024-10-09 12:30:09,874','2024-10-09 12:30:09,878','2024-10-09 12:30:09,895','2024-10-09 12:30:09,904','2024-10-09 12:30:10,070','2024-10-09 12:30:10,071','2024-10-09 12:30:10,072','2024-10-09 12:30:10,106','2024-10-09 12:30:10,245','2024-10-09 12:30:10,325','2024-10-09 12:30:10,382','2024-10-09 12:30:10,398','2024-10-09 12:30:10,502','2024-10-09 12:30:10,866','2024-10-09 12:30:11,018','2024-10-09 12:30:11,082','2024-10-09 12:30:11,089','2024-10-09 12:30:11,243','2024-10-09 12:30:11,246','2024-10-09 12:30:58,627','2024-10-09 12:31:58,627','2024-10-09 12:32:58,625','2024-10-09 12:33:58,626','2024-10-09 12:34:58,633','2024-10-09 12:35:00,028','2024-10-09 12:35:58,634','2024-10-09 12:36:58,626','2024-10-09 12:37:58,629','2024-10-09 12:38:58,626','2024-10-09 12:39:58,634','2024-10-09 12:40:00,021','2024-10-09 12:40:58,634','2024-10-09 12:41:58,634','2024-10-09 12:42:58,640','2024-10-09 12:43:58,642','2024-10-09 12:44:58,653','2024-10-09 12:45:00,031','2024-10-09 12:45:00,291','2024-10-09 12:45:58,626','2024-10-09 12:46:58,635','2024-10-09 12:47:58,651','2024-10-09 12:48:58,633','2024-10-09 12:49:58,632','2024-10-09 12:50:00,020','2024-10-09 12:50:58,636','2024-10-09 12:51:58,639','2024-10-09 12:52:58,635','2024-10-09 12:53:58,645','2024-10-09 12:54:58,640','2024-10-09 12:55:00,030','2024-10-09 12:55:58,635','2024-10-09 12:56:35,872','2024-10-09 12:56:35,962','2024-10-09 12:56:45,994','2024-10-09 12:56:46,118','2024-10-09 12:56:58,630','2024-10-09 12:57:58,632','2024-10-09 12:58:58,626','2024-10-09 12:59:58,640','2024-10-09 13:00:00,040','2024-10-09 13:00:00,285','2024-10-09 13:00:58,643','2024-10-09 13:01:58,627','2024-10-09 13:02:58,649','2024-10-09 13:03:58,637','2024-10-09 13:04:58,630','2024-10-09 13:05:00,016','2024-10-09 13:05:58,627','2024-10-09 13:06:58,633','2024-10-09 13:07:58,637','2024-10-09 13:08:58,637','2024-10-09 13:09:58,639','2024-10-09 13:10:00,035','2024-10-09 13:10:58,627','2024-10-09 13:11:58,635','2024-10-09 13:12:58,635','2024-10-09 13:13:58,648','2024-10-09 13:14:58,648','2024-10-09 13:15:00,020','2024-10-09 13:15:00,242','2024-10-09 13:15:58,626','2024-10-09 13:16:58,633','2024-10-09 13:17:58,625','2024-10-09 13:18:58,634','2024-10-09 13:19:58,645','2024-10-09 13:20:00,041','2024-10-09 13:20:58,629','2024-10-09 13:21:34,278','2024-10-09 13:21:58,636','2024-10-09 13:22:58,634','2024-10-09 13:23:58,632','2024-10-09 13:24:14,697','2024-10-09 13:24:58,626','2024-10-09 13:25:00,029','2024-10-09 13:25:58,645','2024-10-09 13:26:58,636','2024-10-09 13:27:58,631','2024-10-09 13:28:58,637','2024-10-09 13:29:58,627','2024-10-09 13:30:00,018','2024-10-09 13:30:00,246','2024-10-09 13:30:12,837','2024-10-09 13:30:58,627','2024-10-09 13:31:31,547','2024-10-09 13:31:58,629','2024-10-09 13:32:58,637','2024-10-09 13:33:58,632','2024-10-09 13:34:58,631','2024-10-09 13:35:00,012','2024-10-09 13:35:40,550','2024-10-09 13:35:58,627','2024-10-09 13:35:58,957','2024-10-09 13:36:40,817','2024-10-09 13:36:58,633','2024-10-09 13:37:10,682','2024-10-09 13:37:10,754','2024-10-09 13:37:13,330','2024-10-09 13:37:13,349','2024-10-09 13:37:58,621','2024-10-09 13:37:58,660','2024-10-09 13:38:13,160','2024-10-09 13:38:19,770','2024-10-09 13:38:20,263','2024-10-09 13:38:22,258','2024-10-09 13:38:22,493','2024-10-09 13:38:25,258','2024-10-09 13:38:25,319','2024-10-09 13:38:25,391','2024-10-09 13:38:29,663','2024-10-09 13:38:29,682','2024-10-09 13:38:58,632','2024-10-09 13:39:26,944','2024-10-09 13:39:40,036','2024-10-09 13:39:40,176','2024-10-09 13:39:40,181','2024-10-09 13:39:40,228','2024-10-09 13:39:58,631','2024-10-09 13:40:00,019','2024-10-09 13:40:58,638','2024-10-09 13:41:58,627','2024-10-09 13:42:58,645','2024-10-09 13:43:58,627','2024-10-09 13:44:58,625','2024-10-09 13:45:00,029','2024-10-09 13:45:00,251','2024-10-09 13:45:58,645','2024-10-09 13:46:58,640','2024-10-09 13:47:58,639','2024-10-09 13:48:58,635','2024-10-09 13:49:59,491','2024-10-09 13:50:00,029','2024-10-09 13:51:27,501','2024-10-09 13:51:36,242','2024-10-09 13:51:36,366','2024-10-09 13:51:58,637','2024-10-09 13:52:58,636','2024-10-09 13:53:58,634','2024-10-09 13:54:17,803','2024-10-09 13:54:29,999','2024-10-09 13:54:30,015','2024-10-09 13:54:40,512','2024-10-09 13:54:40,540','2024-10-09 13:54:58,647','2024-10-09 13:55:00,042','2024-10-09 13:55:58,628','2024-10-09 13:56:56,319','2024-10-09 13:56:56,412','2024-10-09 13:56:57,478','2024-10-09 13:56:57,601','2024-10-09 13:56:58,634','2024-10-09 13:57:57,776','2024-10-09 13:57:57,878','2024-10-09 13:57:58,628','2024-10-09 13:58:42,001','2024-10-09 13:58:58,624','2024-10-09 13:59:58,627','2024-10-09 14:00:00,041','2024-10-09 14:00:00,320','2024-10-09 14:00:58,625','2024-10-09 14:01:58,637','2024-10-09 14:02:04,772','2024-10-09 14:02:04,788','2024-10-09 14:02:15,045','2024-10-09 14:02:15,051','2024-10-09 14:02:15,269','2024-10-09 14:02:15,311','2024-10-09 14:02:21,725','2024-10-09 14:02:21,765','2024-10-09 14:02:58,640','2024-10-09 14:03:58,628','2024-10-09 14:04:58,630','2024-10-09 14:05:00,028','2024-10-09 14:05:58,159','2024-10-09 14:05:58,630','2024-10-09 14:06:58,636','2024-10-09 14:07:58,625','2024-10-09 14:08:58,640','2024-10-09 14:09:58,643','2024-10-09 14:10:00,024','2024-10-09 14:10:58,626','2024-10-09 14:11:58,632','2024-10-09 14:12:58,641','2024-10-09 14:13:58,635','2024-10-09 14:14:40,089','2024-10-09 14:14:58,624','2024-10-09 14:15:00,023','2024-10-09 14:15:00,291','2024-10-09 14:15:58,626','2024-10-09 14:16:58,639','2024-10-09 14:17:58,626','2024-10-09 14:18:59,655','2024-10-09 14:19:23,221','2024-10-09 14:19:58,795','2024-10-09 14:20:00,032','2024-10-09 14:20:01,713','2024-10-09 14:20:02,405','2024-10-09 14:20:02,406','2024-10-09 14:20:02,407','2024-10-09 14:20:02,408','2024-10-09 14:20:03,195','2024-10-09 14:20:04,400','2024-10-09 14:20:04,503','2024-10-09 14:20:04,590','2024-10-09 14:20:04,666','2024-10-09 14:20:04,674','2024-10-09 14:20:05,905','2024-10-09 14:20:07,187','2024-10-09 14:20:07,336','2024-10-09 14:20:07,487','2024-10-09 14:20:07,498','2024-10-09 14:20:07,594','2024-10-09 14:20:07,637','2024-10-09 14:20:50,642','2024-10-09 14:20:58,636','2024-10-09 14:21:08,553','2024-10-09 14:21:08,734','2024-10-09 14:21:08,735','2024-10-09 14:21:08,808','2024-10-09 14:21:38,673','2024-10-09 14:21:58,638','2024-10-09 14:22:58,641','2024-10-09 14:23:14,403','2024-10-09 14:23:58,633','2024-10-09 14:24:36,981','2024-10-09 14:24:58,640','2024-10-09 14:25:00,025','2024-10-09 14:25:05,703','2024-10-09 14:25:05,714','2024-10-09 14:25:14,200','2024-10-09 14:25:14,204','2024-10-09 14:25:14,436','2024-10-09 14:25:14,473','2024-10-09 14:25:20,083','2024-10-09 14:25:20,125','2024-10-09 14:25:27,676','2024-10-09 14:25:58,627','2024-10-09 14:26:16,708','2024-10-09 14:26:16,832','2024-10-09 14:26:36,920','2024-10-09 14:26:37,010','2024-10-09 14:26:57,118','2024-10-09 14:26:57,253','2024-10-09 14:26:58,627','2024-10-09 14:27:21,247','2024-10-09 14:27:58,626','2024-10-09 14:28:05,478','2024-10-09 14:28:05,482','2024-10-09 14:28:05,578','2024-10-09 14:28:05,587','2024-10-09 14:28:12,083','2024-10-09 14:28:12,187','2024-10-09 14:28:58,630','2024-10-09 14:29:58,635','2024-10-09 14:30:00,040','2024-10-09 14:30:00,268','2024-10-09 14:30:58,635','2024-10-09 14:31:58,635','2024-10-09 14:32:58,626','2024-10-09 14:33:48,391','2024-10-09 14:33:58,633','2024-10-09 14:34:58,632','2024-10-09 14:35:00,013','2024-10-09 14:35:44,893','2024-10-09 14:35:48,995','2024-10-09 14:35:48,998','2024-10-09 14:35:54,152','2024-10-09 14:35:54,154','2024-10-09 14:35:58,647','2024-10-09 14:35:59,849','2024-10-09 14:35:59,851','2024-10-09 14:36:05,853','2024-10-09 14:36:05,858','2024-10-09 14:36:11,858','2024-10-09 14:36:11,859','2024-10-09 14:36:17,855','2024-10-09 14:36:23,844','2024-10-09 14:36:29,856','2024-10-09 14:36:35,853','2024-10-09 14:36:35,856','2024-10-09 14:36:41,853','2024-10-09 14:36:41,860','2024-10-09 14:36:47,850','2024-10-09 14:36:47,871','2024-10-09 14:36:52,935','2024-10-09 14:36:53,071','2024-10-09 14:36:53,861','2024-10-09 14:36:58,625','2024-10-09 14:36:59,867','2024-10-09 14:36:59,871','2024-10-09 14:37:03,065','2024-10-09 14:37:03,189','2024-10-09 14:37:05,846','2024-10-09 14:37:05,862','2024-10-09 14:37:11,868','2024-10-09 14:37:11,873','2024-10-09 14:37:17,977','2024-10-09 14:37:17,980','2024-10-09 14:37:24,155','2024-10-09 14:37:29,859','2024-10-09 14:37:29,860','2024-10-09 14:37:35,859','2024-10-09 14:37:41,856','2024-10-09 14:37:47,854','2024-10-09 14:37:53,854','2024-10-09 14:37:53,856','2024-10-09 14:37:58,636','2024-10-09 14:37:59,848','2024-10-09 14:37:59,854','2024-10-09 14:38:05,862','2024-10-09 14:38:05,864','2024-10-09 14:38:11,862','2024-10-09 14:38:11,866','2024-10-09 14:38:17,856','2024-10-09 14:38:17,859','2024-10-09 14:38:23,880','2024-10-09 14:38:23,881','2024-10-09 14:38:29,876','2024-10-09 14:38:29,877','2024-10-09 14:38:35,854','2024-10-09 14:38:37,303','2024-10-09 14:38:37,368','2024-10-09 14:38:37,442','2024-10-09 14:38:41,852','2024-10-09 14:38:41,856','2024-10-09 14:38:47,863','2024-10-09 14:38:47,865','2024-10-09 14:38:53,865','2024-10-09 14:38:58,648','2024-10-09 14:38:59,838','2024-10-09 14:38:59,842','2024-10-09 14:39:05,856','2024-10-09 14:39:05,857','2024-10-09 14:39:11,869','2024-10-09 14:39:11,874','2024-10-09 14:39:18,176','2024-10-09 14:39:18,180','2024-10-09 14:39:23,861','2024-10-09 14:39:23,875','2024-10-09 14:39:29,858','2024-10-09 14:39:29,871','2024-10-09 14:39:35,854','2024-10-09 14:39:35,858','2024-10-09 14:39:41,861','2024-10-09 14:39:41,873','2024-10-09 14:39:43,784','2024-10-09 14:39:47,865','2024-10-09 14:39:47,866','2024-10-09 14:39:47,900','2024-10-09 14:39:47,970','2024-10-09 14:39:48,030','2024-10-09 14:39:51,073','2024-10-09 14:39:53,865','2024-10-09 14:39:53,869','2024-10-09 14:39:58,630','2024-10-09 14:39:59,848','2024-10-09 14:39:59,851','2024-10-09 14:40:00,029','2024-10-09 14:40:05,888','2024-10-09 14:40:08,165','2024-10-09 14:40:11,853','2024-10-09 14:40:11,861','2024-10-09 14:40:17,863','2024-10-09 14:40:23,862','2024-10-09 14:40:29,869','2024-10-09 14:40:29,880','2024-10-09 14:40:36,166','2024-10-09 14:40:36,170','2024-10-09 14:40:41,849','2024-10-09 14:40:41,851','2024-10-09 14:40:47,866','2024-10-09 14:40:47,869','2024-10-09 14:40:54,470','2024-10-09 14:40:54,486','2024-10-09 14:40:58,625','2024-10-09 14:40:59,843','2024-10-09 14:40:59,846','2024-10-09 14:41:05,873','2024-10-09 14:41:11,852','2024-10-09 14:41:11,854','2024-10-09 14:41:17,876','2024-10-09 14:41:17,880','2024-10-09 14:41:23,862','2024-10-09 14:41:23,863','2024-10-09 14:41:29,858','2024-10-09 14:41:29,862','2024-10-09 14:41:35,863','2024-10-09 14:41:41,858','2024-10-09 14:41:41,860','2024-10-09 14:41:47,856','2024-10-09 14:41:47,860','2024-10-09 14:41:53,865','2024-10-09 14:41:53,868','2024-10-09 14:41:57,046','2024-10-09 14:41:57,167','2024-10-09 14:41:58,634','2024-10-09 14:41:59,851','2024-10-09 14:42:05,859','2024-10-09 14:42:05,867','2024-10-09 14:42:07,048','2024-10-09 14:42:07,163','2024-10-09 14:42:11,857','2024-10-09 14:42:11,862','2024-10-09 14:42:17,879','2024-10-09 14:42:17,880','2024-10-09 14:42:23,851','2024-10-09 14:42:23,853','2024-10-09 14:42:29,869','2024-10-09 14:42:29,870','2024-10-09 14:42:35,871','2024-10-09 14:42:41,854','2024-10-09 14:42:41,859','2024-10-09 14:42:47,888','2024-10-09 14:42:47,892','2024-10-09 14:42:53,859','2024-10-09 14:42:53,863','2024-10-09 14:42:58,640','2024-10-09 14:42:59,837','2024-10-09 14:42:59,861','2024-10-09 14:43:06,134','2024-10-09 14:43:12,138','2024-10-09 14:43:12,139','2024-10-09 14:43:14,949','2024-10-09 14:43:17,851','2024-10-09 14:43:17,853','2024-10-09 14:43:20,846','2024-10-09 14:43:22,406','2024-10-09 14:43:23,855','2024-10-09 14:43:29,849','2024-10-09 14:43:29,851','2024-10-09 14:43:35,853','2024-10-09 14:43:35,861','2024-10-09 14:43:41,856','2024-10-09 14:43:41,860','2024-10-09 14:43:43,867','2024-10-09 14:43:46,276','2024-10-09 14:43:47,866','2024-10-09 14:43:47,870','2024-10-09 14:43:53,861','2024-10-09 14:43:53,863','2024-10-09 14:43:58,627','2024-10-09 14:43:59,836','2024-10-09 14:43:59,849','2024-10-09 14:44:02,445','2024-10-09 14:44:02,517','2024-10-09 14:44:05,906','2024-10-09 14:44:05,909','2024-10-09 14:44:12,164','2024-10-09 14:44:17,853','2024-10-09 14:44:17,855','2024-10-09 14:44:23,874','2024-10-09 14:44:23,875','2024-10-09 14:44:29,859','2024-10-09 14:44:29,862','2024-10-09 14:44:36,162','2024-10-09 14:44:36,165','2024-10-09 14:44:41,866','2024-10-09 14:44:41,887','2024-10-09 14:44:47,870','2024-10-09 14:44:47,876','2024-10-09 14:44:53,861','2024-10-09 14:44:53,864','2024-10-09 14:44:58,633','2024-10-09 14:44:59,865','2024-10-09 14:44:59,873','2024-10-09 14:45:00,013','2024-10-09 14:45:00,294','2024-10-09 14:45:05,858','2024-10-09 14:45:05,861','2024-10-09 14:45:11,880','2024-10-09 14:45:17,849','2024-10-09 14:45:23,855','2024-10-09 14:45:29,845','2024-10-09 14:45:29,847','2024-10-09 14:45:35,858','2024-10-09 14:45:35,860','2024-10-09 14:45:41,840','2024-10-09 14:45:41,841','2024-10-09 14:45:47,887','2024-10-09 14:45:47,888','2024-10-09 14:45:53,851','2024-10-09 14:45:53,852','2024-10-09 14:45:58,631','2024-10-09 14:45:59,838','2024-10-09 14:45:59,839','2024-10-09 14:46:04,709','2024-10-09 14:46:04,810','2024-10-09 14:46:05,856','2024-10-09 14:46:11,843','2024-10-09 14:46:11,848','2024-10-09 14:46:17,859','2024-10-09 14:46:17,860','2024-10-09 14:46:23,864','2024-10-09 14:46:29,859','2024-10-09 14:46:29,861','2024-10-09 14:46:35,851','2024-10-09 14:46:35,854','2024-10-09 14:46:41,863','2024-10-09 14:46:48,179','2024-10-09 14:46:48,184','2024-10-09 14:46:53,860','2024-10-09 14:46:53,869','2024-10-09 14:46:58,636','2024-10-09 14:46:59,849','2024-10-09 14:46:59,851','2024-10-09 14:47:06,139','2024-10-09 14:47:11,867','2024-10-09 14:47:11,875','2024-10-09 14:47:17,877','2024-10-09 14:47:17,884','2024-10-09 14:47:23,878','2024-10-09 14:47:23,883','2024-10-09 14:47:29,865','2024-10-09 14:47:29,872','2024-10-09 14:47:35,870','2024-10-09 14:47:35,874','2024-10-09 14:47:41,863','2024-10-09 14:47:47,876','2024-10-09 14:47:47,878','2024-10-09 14:47:53,862','2024-10-09 14:47:53,870','2024-10-09 14:47:58,624','2024-10-09 14:47:59,846','2024-10-09 14:47:59,847','2024-10-09 14:48:05,858','2024-10-09 14:48:05,861','2024-10-09 14:48:11,855','2024-10-09 14:48:11,865','2024-10-09 14:48:17,852','2024-10-09 14:48:17,861','2024-10-09 14:48:23,851','2024-10-09 14:48:23,853','2024-10-09 14:48:29,858','2024-10-09 14:48:29,859','2024-10-09 14:48:36,162','2024-10-09 14:48:36,171','2024-10-09 14:48:41,831','2024-10-09 14:48:41,848','2024-10-09 14:48:43,710','2024-10-09 14:48:47,868','2024-10-09 14:48:47,871','2024-10-09 14:48:53,858','2024-10-09 14:48:53,861','2024-10-09 14:48:58,624','2024-10-09 14:49:00,068','2024-10-09 14:49:00,102','2024-10-09 14:49:05,851','2024-10-09 14:49:11,867','2024-10-09 14:49:17,858','2024-10-09 14:49:17,867','2024-10-09 14:49:22,393','2024-10-09 14:49:22,491','2024-10-09 14:49:24,146','2024-10-09 14:49:24,150','2024-10-09 14:49:29,861','2024-10-09 14:49:29,864','2024-10-09 14:49:35,854','2024-10-09 14:49:35,858','2024-10-09 14:49:41,854','2024-10-09 14:49:47,880','2024-10-09 14:49:47,887','2024-10-09 14:49:53,860','2024-10-09 14:49:53,861','2024-10-09 14:49:58,630','2024-10-09 14:49:59,858','2024-10-09 14:49:59,859','2024-10-09 14:50:00,021','2024-10-09 14:50:05,851','2024-10-09 14:50:05,852','2024-10-09 14:50:11,884','2024-10-09 14:50:11,885','2024-10-09 14:50:17,428','2024-10-09 14:50:17,435','2024-10-09 14:50:23,151','2024-10-09 14:50:23,153','2024-10-09 14:50:28,852','2024-10-09 14:50:28,901','2024-10-09 14:50:34,848','2024-10-09 14:50:34,857','2024-10-09 14:50:40,843','2024-10-09 14:50:40,856','2024-10-09 14:50:46,860','2024-10-09 14:50:46,863','2024-10-09 14:50:52,882','2024-10-09 14:50:52,883','2024-10-09 14:50:58,654','2024-10-09 14:50:58,850','2024-10-09 14:50:58,854','2024-10-09 14:51:04,851','2024-10-09 14:51:04,852','2024-10-09 14:51:10,858','2024-10-09 14:51:10,861','2024-10-09 14:51:16,843','2024-10-09 14:51:16,845','2024-10-09 14:51:22,847','2024-10-09 14:51:22,851','2024-10-09 14:51:28,848','2024-10-09 14:51:34,850','2024-10-09 14:51:34,851','2024-10-09 14:51:40,835','2024-10-09 14:51:40,837','2024-10-09 14:51:46,839','2024-10-09 14:51:46,843','2024-10-09 14:51:52,861','2024-10-09 14:51:58,631','2024-10-09 14:51:58,847','2024-10-09 14:51:58,848','2024-10-09 14:52:04,844','2024-10-09 14:52:10,851','2024-10-09 14:52:10,854','2024-10-09 14:52:16,838','2024-10-09 14:52:16,839','2024-10-09 14:52:22,864','2024-10-09 14:52:22,867','2024-10-09 14:52:28,857','2024-10-09 14:52:28,860','2024-10-09 14:52:34,840','2024-10-09 14:52:34,849','2024-10-09 14:52:40,850','2024-10-09 14:52:46,867','2024-10-09 14:52:46,870','2024-10-09 14:52:52,870','2024-10-09 14:52:52,871','2024-10-09 14:52:58,632','2024-10-09 14:52:58,850','2024-10-09 14:52:58,852','2024-10-09 14:53:05,170','2024-10-09 14:53:05,173','2024-10-09 14:53:10,845','2024-10-09 14:53:10,848','2024-10-09 14:53:16,848','2024-10-09 14:53:16,854','2024-10-09 14:53:22,854','2024-10-09 14:53:22,872','2024-10-09 14:53:23,911','2024-10-09 14:53:23,915','2024-10-09 14:53:28,853','2024-10-09 14:53:28,857','2024-10-09 14:53:29,851','2024-10-09 14:53:29,853','2024-10-09 14:53:34,856','2024-10-09 14:53:34,857','2024-10-09 14:53:35,849','2024-10-09 14:53:35,850','2024-10-09 14:53:40,864','2024-10-09 14:53:40,868','2024-10-09 14:53:41,837','2024-10-09 14:53:41,850','2024-10-09 14:53:46,863','2024-10-09 14:53:47,860','2024-10-09 14:53:52,999','2024-10-09 14:53:53,000','2024-10-09 14:53:53,870','2024-10-09 14:53:58,633','2024-10-09 14:53:59,132','2024-10-09 14:53:59,424','2024-10-09 14:53:59,858','2024-10-09 14:53:59,869','2024-10-09 14:54:04,851','2024-10-09 14:54:04,857','2024-10-09 14:54:05,847','2024-10-09 14:54:05,850','2024-10-09 14:54:10,850','2024-10-09 14:54:10,860','2024-10-09 14:54:11,848','2024-10-09 14:54:11,854','2024-10-09 14:54:16,840','2024-10-09 14:54:17,857','2024-10-09 14:54:17,859','2024-10-09 14:54:22,850','2024-10-09 14:54:22,853','2024-10-09 14:54:23,882','2024-10-09 14:54:23,897','2024-10-09 14:54:28,861','2024-10-09 14:54:28,867','2024-10-09 14:54:29,849','2024-10-09 14:54:29,851','2024-10-09 14:54:34,860','2024-10-09 14:54:34,861','2024-10-09 14:54:35,839','2024-10-09 14:54:35,840','2024-10-09 14:54:40,869','2024-10-09 14:54:41,857','2024-10-09 14:54:41,860','2024-10-09 14:54:47,135','2024-10-09 14:54:47,137','2024-10-09 14:54:47,846','2024-10-09 14:54:47,857','2024-10-09 14:54:52,877','2024-10-09 14:54:52,886','2024-10-09 14:54:53,846','2024-10-09 14:54:53,848','2024-10-09 14:54:58,631','2024-10-09 14:54:58,849','2024-10-09 14:54:58,851','2024-10-09 14:54:59,852','2024-10-09 14:54:59,855','2024-10-09 14:55:00,029','2024-10-09 14:55:04,854','2024-10-09 14:55:05,846','2024-10-09 14:55:11,174','2024-10-09 14:55:11,184','2024-10-09 14:55:11,841','2024-10-09 14:55:11,847','2024-10-09 14:55:16,863','2024-10-09 14:55:16,864','2024-10-09 14:55:17,842','2024-10-09 14:55:17,845','2024-10-09 14:55:22,857','2024-10-09 14:55:22,861','2024-10-09 14:55:23,840','2024-10-09 14:55:23,852','2024-10-09 14:55:58,636','2024-10-09 14:56:37,045','2024-10-09 14:56:37,134','2024-10-09 14:56:57,476','2024-10-09 14:56:57,596','2024-10-09 14:56:58,636','2024-10-09 14:57:58,640','2024-10-09 14:58:38,238','2024-10-09 14:58:38,346','2024-10-09 14:58:58,631','2024-10-09 14:59:17,576','2024-10-09 14:59:58,624','2024-10-09 15:00:00,056','2024-10-09 15:00:00,295','2024-10-09 15:00:16,301','2024-10-09 15:00:58,901','2024-10-09 15:01:58,624','2024-10-09 15:02:58,634','2024-10-09 15:03:58,624','2024-10-09 15:04:17,394','2024-10-09 15:04:58,643','2024-10-09 15:05:00,022','2024-10-09 15:05:58,634','2024-10-09 15:06:58,628','2024-10-09 15:07:10,038','2024-10-09 15:07:58,651','2024-10-09 15:08:58,645','2024-10-09 15:09:58,624','2024-10-09 15:10:00,026','2024-10-09 15:10:58,632','2024-10-09 15:11:56,350','2024-10-09 15:11:58,626','2024-10-09 15:12:58,624','2024-10-09 15:13:58,631','2024-10-09 15:14:58,633','2024-10-09 15:15:00,020','2024-10-09 15:15:00,243','2024-10-09 15:15:58,624','2024-10-09 15:16:58,627','2024-10-09 15:17:58,636','2024-10-09 15:18:58,629','2024-10-09 15:19:58,633','2024-10-09 15:20:00,029','2024-10-09 15:20:53,508','2024-10-09 15:20:58,623','2024-10-09 15:21:58,633','2024-10-09 15:22:50,872','2024-10-09 15:22:58,629','2024-10-09 15:23:58,622','2024-10-09 15:24:58,623','2024-10-09 15:25:00,021','2024-10-09 15:25:58,626','2024-10-09 15:26:40,477','2024-10-09 15:26:40,574','2024-10-09 15:26:58,624','2024-10-09 15:27:01,100','2024-10-09 15:27:01,210','2024-10-09 15:27:36,685','2024-10-09 15:27:58,623','2024-10-09 15:28:18,419','2024-10-09 15:28:28,784','2024-10-09 15:28:28,785','2024-10-09 15:28:29,049','2024-10-09 15:28:29,082','2024-10-09 15:28:35,131','2024-10-09 15:28:35,190','2024-10-09 15:28:59,647','2024-10-09 15:29:59,649','2024-10-09 15:30:00,030','2024-10-09 15:30:00,240','2024-10-09 15:30:03,179','2024-10-09 15:30:03,182','2024-10-09 15:30:03,183','2024-10-09 15:30:03,502','2024-10-09 15:30:03,503','2024-10-09 15:30:03,504','2024-10-09 15:30:03,589','2024-10-09 15:30:03,590','2024-10-09 15:30:06,693','2024-10-09 15:30:06,911','2024-10-09 15:30:06,933','2024-10-09 15:30:07,001','2024-10-09 15:30:07,019','2024-10-09 15:30:07,115','2024-10-09 15:30:07,255','2024-10-09 15:30:07,257','2024-10-09 15:30:07,261','2024-10-09 15:30:07,307','2024-10-09 15:30:07,331','2024-10-09 15:30:07,496','2024-10-09 15:30:07,516','2024-10-09 15:30:07,933','2024-10-09 15:30:07,967','2024-10-09 15:30:08,082','2024-10-09 15:30:08,220','2024-10-09 15:30:08,237','2024-10-09 15:30:09,135','2024-10-09 15:30:09,290','2024-10-09 15:30:51,902','2024-10-09 15:30:58,629','2024-10-09 15:31:58,629','2024-10-09 15:32:58,284','2024-10-09 15:32:58,631','2024-10-09 15:33:14,569','2024-10-09 15:33:16,518','2024-10-09 15:33:18,915','2024-10-09 15:33:40,155','2024-10-09 15:33:49,760','2024-10-09 15:33:53,815','2024-10-09 15:33:58,626','2024-10-09 15:34:31,158','2024-10-09 15:34:31,229','2024-10-09 15:34:58,625','2024-10-09 15:35:00,015','2024-10-09 15:35:58,628','2024-10-09 15:36:58,628','2024-10-09 15:37:30,874','2024-10-09 15:37:30,978','2024-10-09 15:37:58,625','2024-10-09 15:38:58,627','2024-10-09 15:39:58,634','2024-10-09 15:40:00,034','2024-10-09 15:40:58,638','2024-10-09 15:41:58,634','2024-10-09 15:42:58,634','2024-10-09 15:43:03,205','2024-10-09 15:43:56,768','2024-10-09 15:43:58,638','2024-10-09 15:44:58,625','2024-10-09 15:45:00,021','2024-10-09 15:45:00,245','2024-10-09 15:45:58,633','2024-10-09 15:46:58,627','2024-10-09 15:47:41,602','2024-10-09 15:47:53,511','2024-10-09 15:47:53,723','2024-10-09 15:47:58,642','2024-10-09 15:48:10,162','2024-10-09 15:48:10,384','2024-10-09 15:48:42,253','2024-10-09 15:48:58,623','2024-10-09 15:49:22,375','2024-10-09 15:49:22,495','2024-10-09 15:49:31,467','2024-10-09 15:49:31,674','2024-10-09 15:49:35,811','2024-10-09 15:49:36,035','2024-10-09 15:49:58,625','2024-10-09 15:50:00,018','2024-10-09 15:50:58,626','2024-10-09 15:51:58,624','2024-10-09 15:52:58,635','2024-10-09 15:53:58,632','2024-10-09 15:54:58,672','2024-10-09 15:55:00,020','2024-10-09 15:55:58,630','2024-10-09 15:56:36,640','2024-10-09 15:56:36,743','2024-10-09 15:56:58,634','2024-10-09 15:57:02,053','2024-10-09 15:57:02,368','2024-10-09 15:57:02,484','2024-10-09 15:57:16,279','2024-10-09 15:57:31,707','2024-10-09 15:57:32,575','2024-10-09 15:57:33,124','2024-10-09 15:57:33,145','2024-10-09 15:57:35,636','2024-10-09 15:57:35,753','2024-10-09 15:57:35,760','2024-10-09 15:57:38,765','2024-10-09 15:57:41,836','2024-10-09 15:57:58,624','2024-10-09 15:58:40,822','2024-10-09 15:58:40,850','2024-10-09 15:58:47,140','2024-10-09 15:58:47,293','2024-10-09 15:58:49,115','2024-10-09 15:58:49,116','2024-10-09 15:58:49,140','2024-10-09 15:58:49,145','2024-10-09 15:58:50,258','2024-10-09 15:58:50,312','2024-10-09 15:58:50,330','2024-10-09 15:58:50,359','2024-10-09 15:58:58,543','2024-10-09 15:58:58,637','2024-10-09 15:59:00,493','2024-10-09 15:59:00,565','2024-10-09 15:59:01,703','2024-10-09 15:59:01,710','2024-10-09 15:59:04,227','2024-10-09 15:59:04,235','2024-10-09 15:59:58,634','2024-10-09 16:00:00,039','2024-10-09 16:00:00,264','2024-10-09 16:00:30,692','2024-10-09 16:00:58,645','2024-10-09 16:01:58,638','2024-10-09 16:02:16,581','2024-10-09 16:02:44,584','2024-10-09 16:02:58,633','2024-10-09 16:03:46,593','2024-10-09 16:03:50,814','2024-10-09 16:03:58,624','2024-10-09 16:04:17,997','2024-10-09 16:04:18,079','2024-10-09 16:04:58,624','2024-10-09 16:05:00,038','2024-10-09 16:05:14,029','2024-10-09 16:05:58,633','2024-10-09 16:06:58,649','2024-10-09 16:07:58,629','2024-10-09 16:08:58,633','2024-10-09 16:09:58,625','2024-10-09 16:10:00,039','2024-10-09 16:10:31,047','2024-10-09 16:10:41,966','2024-10-09 16:10:42,042','2024-10-09 16:10:58,625','2024-10-09 16:11:58,625','2024-10-09 16:12:25,753','2024-10-09 16:12:58,634','2024-10-09 16:13:55,422','2024-10-09 16:13:55,514','2024-10-09 16:13:58,629','2024-10-09 16:14:16,889','2024-10-09 16:14:58,637','2024-10-09 16:15:00,036','2024-10-09 16:15:00,241','2024-10-09 16:15:58,626','2024-10-09 16:16:58,632','2024-10-09 16:17:58,633','2024-10-09 16:18:58,637','2024-10-09 16:19:58,639','2024-10-09 16:20:00,029','2024-10-09 16:20:58,635','2024-10-09 16:21:58,623','2024-10-09 16:22:58,637','2024-10-09 16:23:58,630','2024-10-09 16:24:58,627','2024-10-09 16:25:00,020','2024-10-09 16:25:58,626','2024-10-09 16:26:41,034','2024-10-09 16:26:41,136','2024-10-09 16:26:56,157','2024-10-09 16:26:58,634','2024-10-09 16:27:03,044','2024-10-09 16:27:03,159','2024-10-09 16:27:58,629','2024-10-09 16:28:58,624','2024-10-09 16:29:58,634','2024-10-09 16:30:00,048','2024-10-09 16:30:00,337','2024-10-09 16:30:58,634','2024-10-09 16:31:58,644','2024-10-09 16:32:58,636','2024-10-09 16:33:58,643','2024-10-09 16:34:58,626','2024-10-09 16:35:00,044','2024-10-09 16:35:58,640','2024-10-09 16:36:58,632','2024-10-09 16:37:58,639','2024-10-09 16:38:58,634','2024-10-09 16:39:58,643','2024-10-09 16:40:00,033','2024-10-09 16:40:58,623','2024-10-09 16:41:58,638','2024-10-09 16:42:58,639','2024-10-09 16:43:54,881','2024-10-09 16:43:58,626','2024-10-09 16:44:58,633','2024-10-09 16:45:00,020','2024-10-09 16:45:00,236','2024-10-09 16:45:58,635','2024-10-09 16:46:58,644','2024-10-09 16:47:58,634','2024-10-09 16:48:58,642','2024-10-09 16:49:58,626','2024-10-09 16:50:00,024','2024-10-09 16:50:58,627','2024-10-09 16:51:32,246','2024-10-09 16:51:34,606','2024-10-09 16:51:34,807','2024-10-09 16:51:35,828','2024-10-09 16:51:35,943','2024-10-09 16:51:41,327','2024-10-09 16:51:41,754','2024-10-09 16:51:58,622','2024-10-09 16:52:54,537','2024-10-09 16:52:58,645','2024-10-09 16:53:30,531','2024-10-09 16:53:38,298','2024-10-09 16:53:58,632','2024-10-09 16:54:34,687','2024-10-09 16:54:58,633','2024-10-09 16:55:00,015','2024-10-09 16:55:58,633','2024-10-09 16:56:41,914','2024-10-09 16:56:42,009','2024-10-09 16:56:58,649','2024-10-09 16:57:02,223','2024-10-09 16:57:02,360','2024-10-09 16:57:58,625','2024-10-09 16:58:00,743','2024-10-09 16:58:58,635','2024-10-09 16:59:26,505','2024-10-09 16:59:58,623','2024-10-09 17:00:00,057','2024-10-09 17:00:00,286','2024-10-09 17:00:58,633','2024-10-09 17:01:58,629','2024-10-09 17:02:58,644','2024-10-09 17:03:58,641','2024-10-09 17:04:58,633','2024-10-09 17:05:00,022','2024-10-09 17:05:58,632','2024-10-09 17:06:58,624','2024-10-09 17:07:58,632','2024-10-09 17:08:58,632','2024-10-09 17:09:58,625','2024-10-09 17:10:00,025','2024-10-09 17:10:58,634','2024-10-09 17:11:58,636','2024-10-09 17:12:58,624','2024-10-09 17:13:58,639','2024-10-09 17:14:58,632','2024-10-09 17:15:00,030','2024-10-09 17:15:00,236','2024-10-09 17:15:58,635','2024-10-09 17:16:41,446','2024-10-09 17:16:45,826','2024-10-09 17:16:46,041','2024-10-09 17:16:46,068','2024-10-09 17:16:58,625','2024-10-09 17:17:21,194','2024-10-09 17:17:21,434','2024-10-09 17:17:21,467','2024-10-09 17:17:58,627','2024-10-09 17:18:58,624','2024-10-09 17:19:58,634','2024-10-09 17:20:00,020','2024-10-09 17:20:58,632','2024-10-09 17:21:58,624',]
              }
            ],
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  formatter: '{value}'
                }
              }
            ],
            series: [
              {
                name: '处理请求数',
                type: 'line',
                data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ],
                markPoint: {
                  data: [
                    {type: 'max', name: '最大值'},
                    {type: 'min', name: '最小值'}
                  ]
                },
                markLine: {
                  data: [
                    {type: 'average', name: '平均值'}
                  ]
                }
              }
            ]
          };
          minuteChart.setOption(minuteOption);

          


          var methodChart = ec.init(document.getElementById('methodChart'));

          methodChart.setOption({
            title: {
              text: 'Request Method统计'
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b} : {c} ({d}%)'

            },
            legend: {
              x: 'center',
              y: 'bottom',
              data: ['GET', 'POST']
            },
            toolbox: {
              show: true,
              feature: {
                magicType: {
                  show: true,
                  type: ['pie', 'funnel'],
                  option: {
                    funnel: {
                      x: '25%',
                      width: '50%',
                      funnelAlign: 'left'
                    }
                  }
                },
                restore: {
                  show: true,
                  title: "Restore"
                },
                saveAsImage: {
                  show: true,
                  title: "Save Image"
                }
              }
            },
            calculable: true,
            series: [{
              name: '占比',
              type: 'pie',
              radius: '55%',
              center: ['50%', '48%'],
              data: [
                {
                  value: 1478,
                  name: 'GET'
                },
                {
                  value: 0,
                  name: 'POST'
                }
              ]
            }]
          });

          


          
            var costPChart = ec.init(document.getElementById('costPChart'));

            costPChart.setOption({
              title: {
                text: 'Response Time统计'
              },
              tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c} ({d}%)'
              },
              legend: {
                x: 'center',
                y: 'bottom',
                data: ['<50ms', '50~100ms', '100~150ms', '150~200ms', '200~250ms', '250~300ms', '300~350ms', '350~400ms', '400~450ms', '450~500ms', '>500ms']
              },
              toolbox: {
                show: true,
                feature: {
                  magicType: {
                    show: true,
                    type: ['pie', 'funnel'],
                    option: {
                      funnel: {
                        x: '25%',
                        width: '50%',
                        funnelAlign: 'left'
                      }
                    }
                  },
                  restore: {
                    show: true,
                    title: "Restore"
                  },
                  saveAsImage: {
                    show: true,
                    title: "Save Image"
                  }
                }
              },
              calculable: true,
              series: [{
                name: '占比',
                type: 'pie',
                radius: '55%',
                center: ['50%', '48%'],
                data: [
                  {
                    value: 1290,
                    name: '<50ms'
                  },
                  {
                    value: 114,
                    name: '50~100ms'
                  },
                  {
                    value: 6,
                    name: '100~150ms'
                  },
                  {
                    value: 5,
                    name: '150~200ms'
                  },
                  {
                    value: 8,
                    name: '200~250ms'
                  },
                  {
                    value: 9,
                    name: '250~300ms'
                  },
                  {
                    value: 14,
                    name: '300~350ms'
                  },
                  {
                    value: 6,
                    name: '350~400ms'
                  },
                  {
                    value: 2,
                    name: '400~450ms'
                  },
                  {
                    value: 1,
                    name: '450~500ms'
                  },
                  {
                    value: 23,
                    name: '>500ms'
                  }
                ]
              }]
            });
          

          

  </script>


  </body>




</html>