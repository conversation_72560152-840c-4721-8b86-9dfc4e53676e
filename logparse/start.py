# -*- coding:utf-8 -*-
import codecs
import os
import re
import json
import time
import traceback
import datetime
import multiprocessing
import math
from collections import Counter
#from numpy import var, average, percentile
from math import sqrt

from logparse.util import get_dir_files
from logparse.config import config
from concurrent.futures import ThreadPoolExecutor
#from logparse.report import generate_web_log_parser_report
#from logparse.report import generate_web_log_parser_urls
#from logparse.report import update_index_html


class URLData:
    def __init__(self, url=None, pv=None, ratio=None, peak=None):
        self.url = url
        self.pv = pv
        self.ratio = ratio
        self.peak = peak
        self.time = []
        self.cost = []
        self.cost_time = {'p9': None, 'p8': None, 'p5': None, 'avg': None, 'variance': None}

    def get_data(self):
        return {'url': self.url, 'pv': self.pv, 'ratio': self.ratio,
                'peak': self.peak, 'cost_time': self.cost_time}


# 计算平均值
def average(lst):
    return sum(lst) / len(lst) if lst else 0

# 计算方差
def var(lst):
    if len(lst) == 0:
        return 0
    avg = average(lst)
    variance = sum((x - avg) ** 2 for x in lst) / len(lst)
    return variance

# 计算百分位数
def percentile(lst, percentile):
    if not lst:
        return None
    lst_sorted = sorted(lst)
    index = int(len(lst_sorted) * (percentile / 100.0))
    return lst_sorted[min(index, len(lst_sorted) - 1)]

def parse_log_format(target_file=None):
    log_format_index = {}
    if target_file is not None and 'apigateway.log'in target_file:
        log_format_list = config.gateway_log_format.split()
    else:
        log_format_list = config.log_format.split()
    for item in log_format_list:
        if item == 'ip':
            log_format_index.setdefault('ip_index', log_format_list.index(item) + 1)
        if item == 'real_ip':
            log_format_index.setdefault('real_ip_index', log_format_list.index(item) + 1)
        if item == 'datetime':
            log_format_index.setdefault('time_index', log_format_list.index(item) + 1)
        if item == 'url':
            log_format_index.setdefault('url_index', log_format_list.index(item) + 1)
        if item == 'method':
            log_format_index.setdefault('method_index', log_format_list.index(item) + 1)
        if item == 'protocol':
            log_format_index.setdefault('protocol_index', log_format_list.index(item) + 1)
        if item == 'cost':
            log_format_index.setdefault('cost_time_index', log_format_list.index(item) + 1)
        if item == 'status':
            log_format_index.setdefault('status', log_format_list.index(item) + 1)

    if 'real_ip_index' in log_format_index.keys():
        log_format_index.setdefault('host_index', log_format_list.index('real_ip') + 1)
    else:
        log_format_index.setdefault('host_index', log_format_list.index('ip') + 1)

    return log_format_index


def not_static_file(url):
    url_front = url.split('?')[0]
    if url_front.split('.')[-1] not in config.static_file:
        return True
    else:
        return False


def is_ignore_url(url):
    url_front = url.split('?')[0]
    if url_front not in config.ignore_urls:
        return False
    else:
        return True


def get_new_url_with_parameters(origin_url):
    origin_url_list = origin_url.split('?')

    if len(origin_url_list) == 1:
        return origin_url
    url_front = origin_url_list[0]
    url_parameters = sorted(origin_url_list[1].split('&'))
    new_url_parameters = []
    for parameter in url_parameters:
        parameter_list = parameter.split('=')
        key = parameter_list[0]
        if len(parameter_list) == 1:
            new_url_parameters.append(parameter)
        elif key in config.custom_keys:
            new_url_parameters.append(key + '=' + config.custom_parameters.get(key))
        elif key in config.fixed_parameter_keys:
            new_url_parameters.append(parameter)
        else:
            new_url_parameters.append(key + '=' + '{' + key + '}')
    new_url = url_front + '?' + '&amp;'.join(new_url_parameters)
    return new_url


def get_new_url_for_always_parameters(origin_url):
    origin_url_list = origin_url.split('?')

    if len(origin_url_list) == 1:
        return origin_url_list[0]

    url_front = origin_url_list[0]
    url_parameters = sorted(origin_url_list[1].split('&'))
    new_url_parameters = []
    for parameter in url_parameters:
        key = parameter.split('=')[0]
        if key in config.always_parameter_keys:
            new_url_parameters.append(parameter)
    if new_url_parameters:
        new_url = url_front + '?' + '&amp;'.join(new_url_parameters)
    else:
        new_url = url_front
    return new_url


def ignore_url_suffix(origin_url):
    origin_url_list = origin_url.split('?')

    if len(origin_url_list) == 1:
        uri_parameter = None
    else:
        uri_parameter = origin_url_list[1:]

    uri = origin_url_list[0]
    new_uri = uri
    for suffix in config.ignore_url_suffix:
        if uri.endswith(suffix):
            new_uri = uri.replace(suffix, '')
            break
    if uri_parameter:
        return new_uri + '?' + '?'.join(uri_parameter)
    else:
        return new_uri


def get_url(match, log_format):
    origin_url = ignore_url_suffix(match.group(log_format.get('url_index')))
    if config.is_with_parameters:
        url = get_new_url_with_parameters(origin_url)
    else:
        if config.always_parameter_keys:
            url = get_new_url_for_always_parameters(origin_url)
        else:
            url = match.group(origin_url.split('?')[0].split('.json')[0])
    return url


# 定义一个处理任务的函数，供进程调用
def worker(lines,pattern,log_format,cost_time_flag, worker_id, shared_hosts, shared_times, shared_hours, shared_minutes, shared_urls, shared_method_counts, shared_status_codes, shared_cost_time_list):
    """进程执行的任务"""
    start_time = time.time()
    for line in lines:
        match = pattern.match(line)
        if match is None:
            continue
        url = get_url(match, log_format)
        if is_ignore_url(url):
            continue
        match_method = match.group(log_format.get('method_index'))
        match_method = match_method.replace('INFO', 'GET')
        if match_method not in config.support_method:
            continue
        if not_static_file(url):
            shared_hosts.append(match.group(log_format.get('host_index')).split(',')[0])
            log_time = match.group(log_format.get('time_index'))
            shared_times.append(log_time)
            log_time_list = log_time.split(':')
            shared_hours.append(':'.join(log_time_list[0:2]))
            shared_minutes.append(':'.join(log_time_list[0:3]))
#            with shared_method_counts.get_lock():  # 使用锁防止竞争条件
            if match_method == 'POST':
                shared_method_counts['post'] += 1
            if match_method == 'GET':
                shared_method_counts['get'] += 1
            if match_method == 'PUT':
                shared_method_counts['put'] += 1
            if match_method == 'DELETE':
                shared_method_counts['delete'] += 1
            if 'cost_time_index' in log_format.keys():
                request_cost_time = int(float(match.group(log_format.get('cost_time_index'))))
                if cost_time_flag:
                    shared_cost_time_list.append({'time': log_time, 'cost_time': request_cost_time})
                else:
                    shared_cost_time_list.append({'time': '', 'cost_time': request_cost_time})
                
#            if 'status' in log_format.keys():
#                status_code = match.group(log_format.get('status'))
#                if status_code in status_codes.keys():
#                    status_codes[status_code] += 1
#                else:
#                    status_codes.setdefault(status_code, 1)
#            else:
#                status_code = target_file.split("/")[-2].split('-')[0]
#            shared_urls.append(match_method + ' ' + url + ' ' + status_code)

            status_code = match.group(log_format.get('status')) if 'status' in log_format.keys() else target_file.split("/")[-2].split('-')[0]
#            with shared_status_codes.get_lock():
            if status_code in shared_status_codes:
                shared_status_codes[status_code] += 1
            else:
                shared_status_codes[status_code] = 1

            shared_urls.append(match_method + ' ' + url + ' ' + status_code)
    # 结束时间
    end_time = time.time()
    # 打印耗时
    print(f"Worker {worker_id} processed {len(lines)} lines.{end_time - start_time:.2f} seconds")



def parse_log_file(target_file):
    log_format = parse_log_format(target_file)
    # 用户IP
    hosts = []
    # 访问时间
    times = []
    # 访问时间中的小时
    hours = []
    # 访问时间中的分钟
    minutes = []
    # 请求URL
    urls = []
    # 请求响应时间
    cost_time_list = []
    cost_time_flag = False
    cost_time_percentile_flag = False
    if 'cost_time_index' in log_format.keys():
        if config.cost_time_flag:
            cost_time_flag = True
        if config.cost_time_percentile_flag:
            cost_time_percentile_flag = True

    # 请求方法计数器
    method_counts = {'post': 0, 'post_percentile': 0,'put': 0, 'put_percentile': 0,'delete': 0, 'delete_percentile': 0, 'get': 0, 'get_percentile': 0}

    # http status code统计
    status_codes = {}
    gateway_flag = False
    if target_file is not None and 'apigateway.log'in target_file:
        pattern = re.compile(config.gateway_log_pattern)
        gateway_flag = True
    else:
        pattern = re.compile(config.log_pattern)
    start_time = time.time()

    # 第一次读取整个文件，获取对应的请求时间、请求URL、请求方法、用户IP、请求响应时间等数据
    with codecs.open(target_file, 'r', 'utf-8') as f:
        lines = f.readlines()
#        worker(lines,pattern,log_format,True, 1, [], [], [], [], [], method_counts, {}, [])
    for line in lines:
        if gateway_flag and (not " ms " in line or not 'request:' in line):
            continue
        match = pattern.match(line)
        if match is None:
            continue
        url = get_url(match, log_format)
        if is_ignore_url(url):
            continue

        match_method = match.group(log_format.get('method_index'))
        match_method = match_method.replace('INFO','GET')
        if match_method not in config.support_method:
            continue
        if not_static_file(url):
            hosts.append(match.group(log_format.get('host_index')).split(',')[0])
            log_time = time.strftime('%Y-%m-%d %H:%M:%S', time.strptime(match.group(log_format.get('time_index')),'%Y-%m-%d %H:%M:%S,%f'))
#            log_time = match.group(log_format.get('time_index'))
            times.append(log_time)
            log_time_list = log_time.split(':')
            hours.append(':'.join(log_time_list[0:1]))
            minutes.append(':'.join(log_time_list[0:2]))
            if match_method == 'POST':
                method_counts['post'] += 1
            if match_method == 'GET':
                method_counts['get'] += 1
            if match_method == 'PUT':
                method_counts['put'] += 1
            if match_method == 'DELETE':
                method_counts['delete'] += 1
            if 'cost_time_index' in log_format.keys():
                request_cost_time = int(float(match.group(log_format.get('cost_time_index'))))
                if cost_time_flag:
                    cost_time_list.append({'time': log_time, 'cost_time': request_cost_time})
                else:
                    cost_time_list.append({'time': '', 'cost_time': request_cost_time})
            if 'status' in log_format.keys():
                status_code = match.group(log_format.get('status'))
                if status_code in status_codes.keys():
                    status_codes[status_code] += 1
                else:
                    status_codes.setdefault(status_code, 1)
            else:
                status_code = target_file.split("/")[-2].split('-')[0]
            urls.append(match_method + ' ' + url + ' ' + status_code)

#    # 根据文件大小分配任务到多个进程
#    num_workers = math.ceil(multiprocessing.cpu_count() / 4)  # 根据CPU核心数创建进程
#    chunk_size = math.ceil(len(lines) / num_workers)
#    print(num_workers,chunk_size)
#    # 创建一个Manager对象，管理共享变量
#    with multiprocessing.Manager() as manager:
#        shared_hosts = manager.list()
#        shared_times = manager.list()
#        shared_hours = manager.list()
#        shared_minutes = manager.list()
#        shared_urls = manager.list()
#        shared_method_counts = manager.dict({'post': 0, 'get': 0, 'put': 0, 'delete': 0})
#        shared_status_codes = manager.dict()
#        shared_cost_time_list = manager.list()
#
#        # 创建进程池
#        with multiprocessing.Pool(processes=num_workers) as pool:
#            # 为每个进程分配一块任务，并行处理
#            for i in range(num_workers):
#                start_index = i * chunk_size
#                end_index = min((i + 1) * chunk_size, len(lines))
#                pool.apply_async(worker, args=(lines[start_index:end_index],pattern,log_format,cost_time_flag, i, shared_hosts, shared_times, shared_hours, shared_minutes, shared_urls, shared_method_counts, shared_status_codes, shared_cost_time_list))
#
#            # 关闭进程池，等待所有进程完成任务
#            pool.close()
#            pool.join()
#
#        # 记录结束时间
#        end_time = time.time()
#
#        # 输出汇总的结果
##        print(f"Hosts: {list(shared_hosts)}")
##        print(f"Times: {list(shared_times)}")
##        print(f"Hours: {list(shared_hours)}")
##        print(f"Minutes: {list(shared_minutes)}")
##        print(f"URLs: {list(shared_urls)}")
##        print(f"Method counts: {dict(shared_method_counts)}")
##        print(f"Status codes: {dict(shared_status_codes)}")
##        print(f"Cost time list: {list(shared_cost_time_list)}")
#        hosts = list(shared_hosts)
#        times = list(shared_times)
#        hours = list(shared_hours)
#        minutes = list(shared_minutes)
#        cost_time_list = list(shared_cost_time_list)
#        urls = list(shared_urls)
#        method_counts = dict(shared_method_counts)
#        status_codes = dict(shared_status_codes)









    # 结束时间
    end_time = time.time()
    # 打印耗时
    print(f"Total time taken1: {end_time - start_time:.2f} seconds")

    if len(times) > 2:
        cross_time = datetime.datetime.strptime(times[-1].split(',')[0], '%Y-%m-%d %H:%M:%S') - datetime.datetime.strptime(times[0].split(',')[0], '%Y-%m-%d %H:%M:%S')
    else:
        cross_time = None

    # 计算PV、UV、平均请求数、GET/POST占比
    pv = len(times)
    uv = len(set(hosts))
    if pv == 0:
        total_data = {'pv': 0, 'uv': 0, 'response_avg': 0, 'response_peak': 0,
                      'response_peak_time': 0, 'url_data_list': [],
                      'source_file': target_file, 'hours_hits': [], 'minutes_hits': [],
                      'second_hits': [], 'cost_time_list': [], 'cost_time_flag': False,
                      'cost_time_range_percentile': {'r1p': 0, 'r2p': 0, 'r3p': 0, 'r4p': 0, 'r5p': 0, 'r6p': 0,'r7p': 0, 'r8p': 0, 'r9p': 0, 'r10p': 0, 'r11p': 0}, 'method_counts': {},
                      'cost_time_percentile_flag': False,
                      'cost_time_threshold': config.cost_time_threshold, 'cost_time_range': {'r1': 0, 'r2': 0, 'r3': 0, 'r4': 0, 'r5': 0, 'r6': 0,'r7': 0, 'r8': 0, 'r9': 0, 'r10': 0, 'r11': 0},
                      'status_codes': {}}
        return total_data
        
    response_avg = int(pv / len(set(times)))
    method_counts['post_percentile'] = int(method_counts['post'] * 100 / pv)
    method_counts['get_percentile'] = int(method_counts['get'] * 100 / pv)
    method_counts['put_percentile'] = int(method_counts['put'] * 100 / pv)
    method_counts['delete_percentile'] = int(method_counts['delete'] * 100 / pv)

    # 获取每小时、每分钟、每秒的请求数量
    hours_counter = Counter(hours)
    minutes_counter = Counter(minutes)
    times_counter = Counter(times)

    # 获取每秒最大请求数及其请求时间
    response_most_common = times_counter.most_common(1)[0]
    response_peak = response_most_common[1]
    response_peak_time = response_most_common[0]

    # 根据不同URL的PV数量截取较多请求，后续只分析进去排名内的URL
    urls_counter = Counter(urls)
    urls_most_common = urls_counter.most_common(config.urls_most_number)
    # 计算请求占比
    url_data_list = []
    for_url_data_uri_index = []
    # 结束时间
    end_time = time.time()
    # 打印耗时
    print(f"Total time taken2: {end_time - start_time:.2f} seconds")
    for item in urls_most_common:
        if item[1] >= config.urls_pv_threshold:
            ratio = '%0.3f' % float(item[1] * 100 / float(pv))
            url_data_list.append(URLData(url=item[0], pv=item[1], ratio=ratio))
            for_url_data_uri_index.append(item[0])
            continue
        if cross_time and cross_time.seconds < config.urls_pv_threshold_time and item[1] >= config.urls_pv_threshold_min:
            ratio = '%0.3f' % float(item[1] * 100 / float(pv))
            url_data_list.append(URLData(url=item[0], pv=item[1], ratio=ratio))
            for_url_data_uri_index.append(item[0])
            continue
    # 结束时间
    end_time = time.time()
    # 打印耗时
    print(f"Total time taken3: {end_time - start_time:.2f} seconds")
    # 第二次读取文件，以获取特定请求的访问时间及响应时间
    for line in lines:
        if gateway_flag and (not " ms " in line or not 'request:' in line):
            continue        
        match = pattern.match(line)
        if match is None:
            continue
        method = match.group(log_format.get('method_index'))
        method = method.replace('INFO','GET')
        url = get_url(match, log_format)
        url_tmp = [method, url]
        if 'status' in log_format.keys():
            status_code = match.group(log_format.get('status'))
            url_tmp.append(status_code)
        else:
            url_tmp.append(target_file.split("/")[-2].split('-')[0])
        target_url = ' '.join(url_tmp)
        if target_url in for_url_data_uri_index:
            index = for_url_data_uri_index.index(target_url)
            url_data_list[index].time.append(match.group(log_format.get('time_index')))
            if 'cost_time_index' in log_format.keys():
                url_data_list[index].cost.append(float(match.group(log_format.get('cost_time_index'))))
    # 结束时间
    end_time = time.time()
    # 打印耗时
    print(f"Total time taken4: {end_time - start_time:.2f} seconds")
    for url_data in url_data_list:
        # 计算每个特定请求的每秒最大并发
        url_data.peak = Counter(url_data.time).most_common(1)[0][1]

        # 计算每个特定请求的耗时均值，中值，方差，百分位等
        if url_data.cost:
            url_data.cost_time['avg'] = '%0.3f' % float(average(url_data.cost))
            url_data.cost_time['variance'] = int(var(url_data.cost))
            url_data.cost_time['p9'] = '%0.3f' % percentile(url_data.cost, 90)
            url_data.cost_time['p8'] = '%0.3f' % percentile(url_data.cost, 80)
            url_data.cost_time['p5'] = '%0.3f' % percentile(url_data.cost, 50)
    # 统计不同响应时间范围的请求数量
    cost_time_range = {'r1': 0, 'r2': 0, 'r3': 0, 'r4': 0, 'r5': 0, 'r6': 0,
                       'r7': 0, 'r8': 0, 'r9': 0, 'r10': 0, 'r11': 0}
    for cost_time in cost_time_list:
        if cost_time['cost_time'] <= 50:
            cost_time_range['r1'] += 1
        elif 50 < cost_time['cost_time'] <= 100:
            cost_time_range['r2'] += 1
        elif 100 < cost_time['cost_time'] <= 150:
            cost_time_range['r3'] += 1
        elif 150 < cost_time['cost_time'] <= 200:
            cost_time_range['r4'] += 1
        elif 200 < cost_time['cost_time'] <= 250:
            cost_time_range['r5'] += 1
        elif 250 < cost_time['cost_time'] <= 300:
            cost_time_range['r6'] += 1
        elif 300 < cost_time['cost_time'] <= 350:
            cost_time_range['r7'] += 1
        elif 350 < cost_time['cost_time'] <= 400:
            cost_time_range['r8'] += 1
        elif 400 < cost_time['cost_time'] <= 450:
            cost_time_range['r9'] += 1
        elif 450 < cost_time['cost_time'] <= 500:
            cost_time_range['r10'] += 1
        else:
            cost_time_range['r11'] += 1
    # 计算不同响应时间范围的请求占比
    cost_time_range_percentile = {'r1p': 0, 'r2p': 0, 'r3p': 0, 'r4p': 0, 'r5p': 0, 'r6p': 0,
                                  'r7p': 0, 'r8p': 0, 'r9p': 0, 'r10p': 0, 'r11p': 0}
    if cost_time_list:
        total_cost_time_pv = float(len(cost_time_list))
        if cost_time_range['r1']:
            cost_time_range_percentile['r1p'] = '%0.3f' % float(cost_time_range['r1'] * 100 / total_cost_time_pv)
        if cost_time_range['r2']:
            cost_time_range_percentile['r2p'] = '%0.3f' % float(cost_time_range['r2'] * 100 / total_cost_time_pv)
        if cost_time_range['r3']:
            cost_time_range_percentile['r3p'] = '%0.3f' % float(cost_time_range['r3'] * 100 / total_cost_time_pv)
        if cost_time_range['r4']:
            cost_time_range_percentile['r4p'] = '%0.3f' % float(cost_time_range['r4'] * 100 / total_cost_time_pv)
        if cost_time_range['r5']:
            cost_time_range_percentile['r5p'] = '%0.3f' % float(cost_time_range['r5'] * 100 / total_cost_time_pv)
        if cost_time_range['r6']:
            cost_time_range_percentile['r6p'] = '%0.3f' % float(cost_time_range['r6'] * 100 / total_cost_time_pv)
        if cost_time_range['r7']:
            cost_time_range_percentile['r7p'] = '%0.3f' % float(cost_time_range['r7'] * 100 / total_cost_time_pv)
        if cost_time_range['r8']:
            cost_time_range_percentile['r8p'] = '%0.3f' % float(cost_time_range['r8'] * 100 / total_cost_time_pv)
        if cost_time_range['r9']:
            cost_time_range_percentile['r9p'] = '%0.3f' % float(cost_time_range['r9'] * 100 / total_cost_time_pv)
        if cost_time_range['r10']:
            cost_time_range_percentile['r10p'] = '%0.3f' % float(cost_time_range['r10'] * 100 / total_cost_time_pv)
        if cost_time_range['r11']:
            cost_time_range_percentile['r11p'] = '%0.3f' % float(cost_time_range['r11'] * 100 / total_cost_time_pv)
    # 结束时间
    end_time = time.time()
    # 打印耗时
    print(f"Total time taken5: {end_time - start_time:.2f} seconds")
    total_data = {'pv': pv, 'uv': uv, 'response_avg': response_avg, 'response_peak': response_peak,
                  'response_peak_time': response_peak_time, 'url_data_list': url_data_list,
                  'source_file': target_file, 'hours_hits': hours_counter, 'minutes_hits': minutes_counter,
                  'second_hits': times_counter, 'cost_time_list': cost_time_list, 'cost_time_flag': cost_time_flag,
                  'cost_time_range_percentile': cost_time_range_percentile, 'method_counts': method_counts,
                  'cost_time_percentile_flag': cost_time_percentile_flag,
                  'cost_time_threshold': config.cost_time_threshold, 'cost_time_range': cost_time_range,
                  'status_codes': status_codes}
#    generate_web_log_parser_report(total_data)
    return total_data


def parse_log_file_with_goaccess(target_file):
    source_file = '../data/' + target_file
    goaccess_file = '../result/report/' + target_file + '_GoAccess.html'
    command = """ goaccess -f %(file)s  -a -q \
            --time-format=%(time_format)s \
            --date-format=%(date_format)s \
            --log-format='%(goaccess_log_format)s' \
            --no-progress > %(goaccess_file)s""" \
              % {'file': source_file, 'time_format': config.time_format, 'date_format': config.date_format,
                 'goaccess_log_format': config.goaccess_log_format, 'goaccess_file': goaccess_file}
    print(command)             
    os.system(command)


def main_BAK():
    log_format = parse_log_format()

    result_files = [result_file.replace('.html', '') for result_file in get_dir_files('../result/report/')]
#    target_files = sorted([data_file for data_file in get_dir_files('../data') if data_file not in result_files])
    target_files = sorted([data_file for data_file in get_dir_files('../data')])

    for target_file in target_files:
        try:
            print(datetime.datetime.now(), ' Start parse file : ' + target_file)

            parse_log_file(target_file)
            if config.goaccess_flag:
                parse_log_file_with_goaccess(target_file)

            print(datetime.datetime.now(), ' End parse file: ' + target_file)
        except Exception:
            exstr = traceback.format_exc()
            print(exstr)
#    update_index_html()


if __name__ == '__main__':
    main()
