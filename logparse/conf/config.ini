[format]
#log-pattern=(\S+)\s-\s-\s\[([^]]+)\s\S+]\s"(\w+)\s(\S+)\s([^"]+)"\s(\d+)\s(\S+)\s(\S+)\s(\S+)\s"([^"]+)"\s"([^"]+)"\s"([^"]+)"\s(\S+)\s"([^"]+)"\s(\S+).*
#log-format=ip datetime method url protocol status business_status instance_id length referer agent real_ip cost host hostname

#log-pattern=(\S+)\s\S+\s(\S+)\s(\S+)\s(\d+)\s(\S+)\s(\S+)\s(\S+)\s(\S+)\s(\S+)\s(\S+)
#log-format=datetime method url status protocol business_status cost host hostname real_ip
#log-pattern=(\S+)\s(\S+)\s\(TLogWeb.*\[(\S+)\] \[(\S+)\]结束URL\[(\S+)\]的调用,耗时为:(\S+)毫秒
#log-format=datetime method real_ip status url cost
log-pattern=(\S+\s\S+)\s(\S+)\s(\S+)\s(\S+).*结束URL\[(\S+)\]的调用,耗时为:(\S+)毫秒
log-format=datetime method real_ip host url cost
gateway-log-pattern=(\S+\s\S+).*\[(\S+)\].*module:\s(\S+)\s.request:\s(\S+)\s(\S+).*=\s(\S+)\sms.*
gateway-log-format=datetime real_ip status method url cost

[filter]
# 支持的方法
# supported request method, otherwise no statistics
support_method=POST,GET,DELETE,PUT,OPTIONS,HEAD,CONNECT

# 是否带参数进行分析（但会包括awalys_parameter_keys所指定的参数）
# enable flag for statistics url parameter，By default, the parameters in the URL will be converted, such as? Key = 123 when converting to key = {key}
is_with_parameters=0
always_parameter_keys=action

# 访问量排行最大请求数量
# the maximum number of URLs separately counted
urls_most_number=200

# 访问量排行的最低PV阀值，低于该阀值的不会进入访问量排行
# The lowest PV threshold of the traffic ranking. Those below this threshold will not enter the traffic ranking.
urls_pv_threshold=1

# 当日志统计时长小于urls_pv_threshold_time的情况下，将会使用urls_pv_threshold_min作为最低PV阀值
# When the log statistics duration is less than urls_pv_threshold_time, urls_pv_threshold_min will be used as the minimum PV threshold
urls_pv_threshold_time=6
urls_pv_threshold_min=1

# 忽略的url的后缀进行统计，如请求是/customer/get/list.json，将会重写为/customer/get/list进行统计
# Ignored URL suffixes for statistics
ignore_url_suffix=.json

# 固定的参数，但is_with_parameters=1时，不会替换一下key的值
# configure special parameter key values, separated by commas, when configured key = 123,  will not be replaced with key = {key}
fixed_parameter_keys=action,submitType,reportType

# 自定义的参数转换
# configure special parameter key-value pairs, separated by commas, when configured t = {timeStamp}, the value of t key will be replaced with a fixed {timeStamp}
custom_parameters=t={timeStamp},v={timeStamp},_={timeStamp}

# 忽略的URL
# urls will be ignore
ignore_urls=/slb.html,/server-status,/httpstatus.html,/server-status-dinghuo/,/server-status-dinghuo

# 忽略的请求类型
# file suffixes will be ignore
static-file=css,CSS,dae,DAE,eot,EOT,gif,GIF,ico,ICO,jpeg,JPEG,jpg,JPG,js,JS,map,MAP,mp3,MP3,pdf,PDF,png,PNG,svg,SVG,swf,SWF,ttf,TTF,txt,TXT,woff,WOFF

[report]
# 报告语言, chinese or english, default chinese
language=chinese

# 是否开启每秒PV曲线图
# flags for PV curve graph per second
second_line_flag=0

# 是否开启耗时占比分布图
# flags for the time-consuming proportion distribution map
cost_time_percentile_flag=1

# 是否开启耗时分布图
# flags for response time interval proportion map and response time distribution map
cost_time_flag=0

# 耗时阈值，超过该值的请求会标红
# Time-consuming threshold, requests exceeding this value are marked red
cost_time_threshold=200.00

# 是否上传数据
# upload flag and upload url
upload_flag=0
upload_url=http://192.168.1.181:5000/logs/upload/

[goaccess]
goaccess_flag=0
time-format=%H:%M:%S
date-format=%d/%b/%Y
goaccess-log-format=%h %^[%d:%t %^] "%r" %s %b "%R" "%u"