#! /usr/bin/python3.6

# -*- coding: gbk -*-

import os
import subprocess
import sys
import argparse
current_directory = os.getcwd()

def check_maven_project():
    """��鵱ǰĿ¼�Ƿ���Maven��Ŀ�ĸ�Ŀ¼��"""
    if not os.path.isfile('pom.xml'):
        print("����: ��ǰĿ¼����һ��Maven��Ŀ��Ŀ¼���Ҳ���pom.xml�ļ���")
        sys.exit(1)

def compile_maven_project():
    """����Maven��Ŀ������JAR����"""
    try:
        print("���ڱ���Maven��Ŀ...")
        # ���� `mvn clean package` ������������Ŀ������JAR��
        subprocess.check_call(['mvn', 'clean', 'package'])
        print("������ɡ�")
    except subprocess.CalledProcessError as e:
        print(f"����ʧ��: {e}")
        sys.exit(1)

def find_jar_file():
    """�������ɵ�JAR���ļ���"""
    jar_path = None

    # ������ǰĿ¼��������service��boot��β��Ŀ¼
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name.endswith('service') or dir_name.endswith('boot'):
                # ���ҵ���Ŀ¼�в���targetĿ¼�µ�JAR�ļ�
                target_dir = os.path.join(root, dir_name, 'target')
                if os.path.exists(target_dir):
                    for file in os.listdir(target_dir):
                        if file.endswith('.jar'):
                            jar_path = os.path.join(target_dir, file)
                            break
                if jar_path:
                    break
        if jar_path:
            break
    return jar_path

def find_config():
    """����config��ַ��"""
    cmd = "kubectl get svc -n incloud|grep incloudmanager-config"
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.read()
    res = result.decode('utf-8').split()
    if len(res) > 3:
        return res[2]
    return None

def get_jar_from_pod(pod):
    """����config��ַ��"""
    cmd = "kubectl get pods -n incloud|grep %s"%(pod)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.readlines()
    res = result[0].decode('utf-8').split()
    if len(res) > 3:
        cmd = "kubectl -n incloud cp %s:/deployments/incloudos ./"%(res[0])
        popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
        result = popen.stdout.read()
        cmd = "ls -rlt|grep *.jar"
        popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
        result = popen.stdout.readlines()
        print(result)
        res = result[-1].decode('utf-8').split()
        if len(res) > 3:
            return res[-1]
    return None

def run_jar(port,debug_port):
    """�������ɵ�JAR����������˿ڲ�����"""
    jar_path = find_jar_file()
    config_ip = find_config()
    config_uri = ""
    if config_ip is not None:
        config_uri = "--spring.cloud.config.uri=http://%s:32001"%config_ip
    if jar_path is None:
        print("δ�ҵ�JAR������ȷ����Ŀ�ѳɹ����롣")
        sys.exit(1)

    print(f"��������JAR��: {jar_path}���˿�: {port}��DEBUG�˿�: {debug_port}")

    try:
        # ����JAR����������˿ڲ���
        #java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=%s -jar istorage-boot/target/*.jar  --spring.profiles.active=k8s --server.port=%s --spring.cloud.config.uri=http://127.0.0.1:32001
        subprocess.check_call(['java', '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=' + str(debug_port) ,'-jar', jar_path, '--spring.profiles.active=k8s', '--server.port=' + str(port) ,config_uri])
    except subprocess.CalledProcessError as e:
        print(f"����ʧ��: {e}")
        sys.exit(1)

def deployPod():
    jarName = None
    cmd = "ls %s|grep SNAPSHOT.jar$ |awk '{print $1}'"%(current_directory)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    res = popen.stdout.read()
    if len(res) > 1:
        res = res.decode("utf-8").strip()
        podName = res.split("-")[0].strip()
        projectName = "%s-%s"%(res.split("-")[0],res.split("-")[1]).strip()
        jarName = res
    else:
        cmd = "ls %s|grep service |awk '{print $1}'"%(current_directory)
        popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
        res = popen.stdout.read()
        if len(res) > 1:
            res = res.decode("utf-8")
            projectName = res.strip()
            podName = res.split("-")[0]    .strip()
        else:
            cmd = "ls %s|grep boot |awk '{print $1}'"%(current_directory)
            popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
            res = popen.stdout.read()
            if len(res) <= 1 or "istorage" not in res.decode("utf-8"):
                print ('usage: use cmd in project ' )
                return
            res = res.decode("utf-8")
            projectName=res.strip()
            podName=res.split("-")[0] .strip()
    if jarName is None:
#        cmd = "cd %s && mvn clean && mvn package"%(current_directory)
#        print (cmd)
#        os.system(cmd)
        # ��鵱ǰĿ¼�Ƿ���Maven��Ŀ
        check_maven_project()
        # ����Maven��Ŀ������JAR
        compile_maven_project()
        
    print(projectName)
    cmd = "kubectl get pods -n incloud -owide|grep %s"%podName
    print (cmd)
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    res = popen.stdout.readlines()
    for row in res:
        pod = row.split()[0].decode("utf-8")
        nod = row.split()[-3].decode("utf-8")
        if jarName is not None:
            cmd = """kubectl cp -n incloud %s/%s %s:/deployments/incloudos/ """%(current_directory,jarName,pod)
        else:
            cmd = """kubectl cp -n incloud %s/%s/target/%s-*-SNAPSHOT.jar %s:/deployments/incloudos/ """%(current_directory,projectName,projectName,pod)
        print (cmd)
        popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
        res = popen.stdout.read()
        cmd = """ssh %s "docker ps |grep k8s_%s" """%(nod,podName)
        popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
        docker = popen.stdout.read()
        print (docker)
        cmd = """ssh %s "docker restart %s" """%(nod,docker.split()[0].decode("utf-8"))
        print(cmd)
        popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
        docker = popen.stdout.read()
        cmd =  "kubectl get pods -n incloud -owide|grep %s"%podName
        os.system(cmd)
        cmd =  "kubectl logs -f -n incloud %s --tail 100"%pod
        os.system(cmd)

def start(args):
    try:
        # ����JAR����������˿ڲ���
        os.system("/usr/sbin/reloadhosts")
    except Exception as e:
        print(f"����ʧ��: {e}")
    # ���˿��Ƿ�Ϊ��Ч������
    port = args.port
    if port is None or port <= 0:
        print("��������Ч�Ķ˿ںš�--port <�˿ں�>")
        sys.exit(1)
    debug_port = port + 20
    if args.debug_port is not None:
        debug_port = args.debug_port
    pod = args.pod
    build = args.build
    if pod is not None:
        jar_path = get_jar_from_pod(pod)
        config_ip = find_config()
        config_uri = ""
        if config_ip is not None:
            config_uri = "--spring.cloud.config.uri=http://%s:32001"%config_ip
        try:
            # ����JAR����������˿ڲ���
            #java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=%s -jar istorage-boot/target/*.jar  --spring.profiles.active=k8s --server.port=%s --spring.cloud.config.uri=http://127.0.0.1:32001
            subprocess.check_call(['java', '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=' + str(debug_port) ,'-jar', jar_path, '--spring.profiles.active=k8s', '--server.port=' + str(port) ,config_uri])
        except subprocess.CalledProcessError as e:
            print(f"����ʧ��: {e}")
            sys.exit(1)
    elif build is not None:
        deployPod()
    else:
        # ��鵱ǰĿ¼�Ƿ���Maven��Ŀ
        check_maven_project()
        # ����Maven��Ŀ������JAR
        compile_maven_project()
        run_jar(port,debug_port)

def main():
    parser = argparse.ArgumentParser(description='��������',
                                     formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    parser.add_argument("--port",type=int,required=True,help='����˿�')
    parser.add_argument("--debug_port",type=int,help='debug�˿�')
    parser.add_argument("--pod",type=str,help='���빤�����ƿ����Զ�����pod�е�jar����')
    parser.add_argument("--build",type=str,help='���빤�����ƿ����Զ��滻pod�е�jar����������docker')
    args = parser.parse_args()
    print(args)
    start(args)
    
if __name__ == '__main__':
    main()
#    if len(sys.argv) != 2:
#        print("�÷�: python run_project <port>")
#        sys.exit(1)
#
#    port = sys.argv[1]
#
#    # ���˿��Ƿ�Ϊ��Ч������
#    if not port.isdigit() or int(port) <= 0:
#        print("��������Ч�Ķ˿ںš�")
#        sys.exit(1)
#    debug_port = int(port) + 20
#    # ��鵱ǰĿ¼�Ƿ���Maven��Ŀ
#    check_maven_project()
#
#    # ����Maven��Ŀ������JAR
#    compile_maven_project()
#    run_jar(port)
