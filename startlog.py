#! /usr/bin/python3.6

# -*- coding: utf-8 -*-
import os,sys,time


if __name__ == "__main__":
    current_directory = os.getcwd()
    filename = sys.argv[0]; 
    if len(sys.argv)< 2: 
        print ('usage: ' + filename + ' port  logpath' )
        sys.exit() 
    if len(sys.argv) > 2: 
        current_directory = sys.argv[2]
    os.system("python3 /root/.tmp/tracelog/flask_ser.py %s %s"%(sys.argv[1],current_directory))