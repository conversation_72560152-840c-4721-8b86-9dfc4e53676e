#! /usr/bin/python3.6

# -*- coding: gbk -*-

import os
import subprocess
import sys

def check_maven_project():
    """��鵱ǰĿ¼�Ƿ���Maven��Ŀ�ĸ�Ŀ¼��"""
    if not os.path.isfile('pom.xml'):
        print("����: ��ǰĿ¼����һ��Maven��Ŀ��Ŀ¼���Ҳ���pom.xml�ļ���")
        sys.exit(1)

def compile_maven_project():
    """����Maven��Ŀ������JAR����"""
    try:
        print("���ڱ���Maven��Ŀ...")
        # ���� `mvn clean package` ������������Ŀ������JAR��
        subprocess.check_call(['mvn', 'clean', 'package'])
        print("������ɡ�")
    except subprocess.CalledProcessError as e:
        print(f"����ʧ��: {e}")
        sys.exit(1)

def find_jar_file():
    """�������ɵ�JAR���ļ���"""
    jar_path = None

    # ������ǰĿ¼��������service��boot��β��Ŀ¼
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name.endswith('service') or dir_name.endswith('boot'):
                # ���ҵ���Ŀ¼�в���targetĿ¼�µ�JAR�ļ�
                target_dir = os.path.join(root, dir_name, 'target')
                if os.path.exists(target_dir):
                    for file in os.listdir(target_dir):
                        if file.endswith('.jar'):
                            jar_path = os.path.join(target_dir, file)
                            break
                if jar_path:
                    break
        if jar_path:
            break

    return jar_path

def find_config():
    """����config��ַ��"""
    cmd = "kubectl get svc -n incloud|grep incloudmanager-config"
    popen = subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
    result = popen.stdout.read()
    res = result.decode('utf-8').split()
    if len(res) > 3:
        return res[2]
    return None

def run_jar(port):
    """�������ɵ�JAR����������˿ڲ�����"""
    jar_path = find_jar_file()
    config_ip = find_config()
    config_uri = ""
    if config_ip is not None:
        config_uri = "--spring.cloud.config.uri=http://%s:32001"%config_ip
    if jar_path is None:
        print("δ�ҵ�JAR������ȷ����Ŀ�ѳɹ����롣")
        sys.exit(1)

    print(f"��������JAR��: {jar_path}���˿�: {port}��DEBUG�˿�: {debug_port}")

    try:
        # ����JAR����������˿ڲ���
        #java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=%s -jar istorage-boot/target/*.jar  --spring.profiles.active=k8s --server.port=%s --spring.cloud.config.uri=http://127.0.0.1:32001
        subprocess.check_call(['java', '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=' + str(debug_port) ,'-jar', jar_path, '--spring.profiles.active=k8s', '--server.port=' + str(port) ,config_uri])
    except subprocess.CalledProcessError as e:
        print(f"����ʧ��: {e}")
        sys.exit(1)

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("�÷�: python run_project <port>")
        sys.exit(1)
