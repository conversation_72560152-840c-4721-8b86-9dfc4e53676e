---- Table: dsc_admin_user
--
--DROP TABLE dsc_admin_user;
--
--CREATE TABLE dsc_admin_user
--(
--  user_id INTEGER PRIMARY KEY AUTOINCREMENT,
--  user_name character varying(60) ,
--  parent_id boolean  DEFAULT false,
--  ru_id bigint  DEFAULT 0,
--  email character varying(60) ,
--  password character varying(32) ,
--  ec_salt character varying(10),
--  add_time integer  DEFAULT 0,
--  last_login integer  DEFAULT 0,
--  last_ip character varying(15) ,
--  action_list text ,
--  nav_list text ,
--  lang_type character varying(50) ,
--  agency_id integer ,
--  suppliers_id integer DEFAULT 0,
--  todolist text,
--  role_id smallint,
--  major_brand integer  DEFAULT 0,
--  admin_user_img character varying(255) ,
--  recently_cat character varying(255) ,
--  admin_type smallint DEFAULT 0,
--  company_code character varying(255)
----  CONSTRAINT dsc_admin_user_pkey PRIMARY KEY (user_id)
--)
--DROP TABLE huanjing;
--CREATE TABLE huanjing
--(
--  id INTEGER PRIMARY KEY AUTOINCREMENT,
--  hostname character varying(60) ,
--  ip character varying(60),
--  port character varying(60),
--  type character varying(60) ,
--  bz1 character varying(60) ,
--  bz2 character varying(60) ,
--  bz3 character varying(60) 
--);
--DROP TABLE data;
--
--CREATE TABLE data
--(
--  id INTEGER PRIMARY KEY AUTOINCREMENT,
--  hj_id character varying(60) ,
--  yhzhlx character varying(60),
--  yhzh character varying(60),
--  khmc character varying(60) ,
--  zjlx character varying(60) ,
--  zjhm character varying(60) ,
--  sjhm character varying(60) ,
--  sm character varying(60) ,
--  hxhj character varying(60) ,
--  bz character varying(60) ,
--  bz1 character varying(60) ,
--  bz2 character varying(60) ,
--  bz3 character varying(60) 
--);