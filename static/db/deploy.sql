--DROP TABLE operate_log;
CREATE TABLE operate_log
(
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  ip character varying(60),
  method character varying(60),
  status_code character varying(60),
  updatetime character varying(60) ,
  url character varying(256) ,
  param character varying(512) ,
  referer character varying(256) ,
  agent character varying(256) ,
  bz1 character varying(60) ,
  bz2 character varying(60) ,
  bz3 character varying(60) 
);

--DROP TABLE install_log;
CREATE TABLE install_log
(
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  ip character varying(60),
  updatetime character varying(60) ,
  status character varying(256) ,
  bz1 character varying(60) ,
  bz2 character varying(60) ,
  bz3 character varying(60) 
);

--DROP TABLE admin_user;
CREATE TABLE admin_user
(
  user_id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_name character varying(60) ,
  parent_id boolean  DEFAULT false,
  ru_id bigint  DEFAULT 0,
  email character varying(60) ,
  password character varying(32) ,
  ec_salt character varying(10),
  add_time integer  DEFAULT 0,
  last_login integer  DEFAULT 0,
  last_ip character varying(15) ,
  action_list text ,
  nav_list text ,
  lang_type character varying(50) ,
  agency_id integer ,
  suppliers_id integer DEFAULT 0,
  todolist text,
  role_id smallint,
  major_brand integer  DEFAULT 0,
  admin_user_img character varying(255) ,
  recently_cat character varying(255) ,
  admin_type smallint DEFAULT 0,
  company_code character varying(255)
)