INSERT INTO `ibaremetal`.`t_baremetal_node`(`id`, `domain_id`, `center_id`, `node_mor`, `name`, `bmc_ip`, `bmc_user_password`, `ipmi_terminal_port`, `power_state`, `provision_state`, `console_enabled`, `deploy_info`, `properties`, `vcpus`, `memory`, `root_disk`, `maintenance`, `maintenance_reason`, `instance_id`, `instance_mor`, `driver`, `network_interface`, `console_interface`, `create_time`, `update_time`, `last_error`, `cpu_arch`, `capabilities`, `smart_card_enable`, `smart_card_ip`, `smart_card_username`, `smart_card_password`, `arch`, `vendor`, `source`, `resource_class`, `gpu_detail`) VALUES ('002d530f-fef0-42ab-8762-8a48bd21e19d', 'domainid', '8ef85ea5-3a97-4fc5-a2e3-75c0c2fcd226', 'dc1dc4f5-4038-4590-939b-dc7bcc518f60', 'bm-haiguang-204', '*************', '{\"ipmi_username\":\"admin\",\"ipmi_password\":\"s0:t|/7KERovuHIospb3ADqTB4Q==;f91a62a0811b021881d6e6a39f37d027\"}', 0, 'power on', 'active', 0, '{\"deploy_ramdisk\":\"ab52083e-e1c7-429e-b39e-097fa01388b3\",\"deploy_kernel\":\"f9d19289-c0de-48dc-89e2-ae6d226a0d3a\"}', '{\"cpu_detail\":\"2 * (Chengdu Hygon Hygon C86 7265 24-core Processor) , 96 cores\",\"local_gb\":\"1786\",\"gpu_detail\":\"\",\"cpus\":\"96\",\"memory_detail\":\"16*32 GiB DDR4 , 512 GiB\",\"gpu_type\":\"\",\"fru\":{\"Product_Name\":\"CS5280H\",\"Product_Manufacturer\":\"Inspur\",\"Product_Serial\":\"21A659619\"},\"disk_detail\":\"1.745 TiB (4*893.750 GiB SSD SATA RAID6) + 1.745 TiB (4*893.750 GiB SSD SATA RAID6)\",\"memory_mb\":\"524288\",\"custom_detail\":\"\",\"gpu_num\":\"\",\"tenant_ipmi_user\":{\"name\":\"clouduser\",\"key\":\"JAg2?NSG\"}}', '96', '524288', '1786', 0, NULL, '8a6e92768f12e8db018f152757670006', 'c250aaa1-55c1-4066-a7e6-5e697e77797d', 'ipmi', 'neutron', NULL, '2024-04-25 18:22:16', '2024-04-25 20:02:37', NULL, 'x86_64', 'iscsi_boot:True,boot_mode:bios,cpu_vt:true,cpu_aes:true,cpu_hugepages:true,cpu_hugepages_1g:true', 0, NULL, NULL, '******', 'x86_64', 'Hygon', 'REGISTER', '5B088385FE9D24B041B86A1C09C3EC89', '');
INSERT INTO `ibaremetal`.`t_baremetal_node`(`id`, `domain_id`, `center_id`, `node_mor`, `name`, `bmc_ip`, `bmc_user_password`, `ipmi_terminal_port`, `power_state`, `provision_state`, `console_enabled`, `deploy_info`, `properties`, `vcpus`, `memory`, `root_disk`, `maintenance`, `maintenance_reason`, `instance_id`, `instance_mor`, `driver`, `network_interface`, `console_interface`, `create_time`, `update_time`, `last_error`, `cpu_arch`, `capabilities`, `smart_card_enable`, `smart_card_ip`, `smart_card_username`, `smart_card_password`, `arch`, `vendor`, `source`, `resource_class`, `gpu_detail`) VALUES ('5cabe75b-72a6-4d1c-af3e-2fb081eb4d58', 'domainid', '8ef85ea5-3a97-4fc5-a2e3-75c0c2fcd226', '7fc315a9-bffb-4b76-990c-d28065bdb44c', 'bm-ft-164', '*************', '{\"ipmi_username\":\"inspur\",\"ipmi_password\":\"s0:t|kFlXLC+IvnOe2RiMNTzoww==;eb7a8d3cd5b6add39cce4f71c4ba6cd3\"}', 0, 'power on', 'active', 0, '{\"deploy_ramdisk\":\"8e89cf69-4767-42fe-afdf-5c5bce61118e\",\"deploy_kernel\":\"f0cd7d77-f17a-44e7-833e-936ac0e2ccfc\"}', '{\"cpu_detail\":\"2 * (Phytium S2500) , 128 cores\",\"local_gb\":\"446\",\"gpu_detail\":\"\",\"cpus\":\"128\",\"memory_detail\":\"16*16 GiB DDR4 , 256 GiB\",\"gpu_type\":\"\",\"fru\":{\"Product_Name\":\"CS5260F\",\"Product_Manufacturer\":\"Inspur\",\"Product_Serial\":\"122004220\"},\"disk_detail\":\"1.82 TiB (1*1863 GiB HDD SATA RAID0) + 447 GiB SSD * 1\",\"memory_mb\":\"262144\",\"custom_detail\":\"\",\"gpu_num\":\"\",\"tenant_ipmi_user\":{\"name\":\"clouduser\"}}', '128', '262144', '446', 0, NULL, '8a6c3e1d8f1e3e9a018f1ee15afa0001', '3b2fb314-20e9-48dd-9006-1f05975bab43', 'ipmi', 'neutron', NULL, '2024-04-25 18:24:10', '2024-04-25 19:15:20', NULL, 'aarch64', 'iscsi_boot:True,boot_mode:uefi', 0, NULL, NULL, '******', 'aarch64', 'FeiTeng', 'REGISTER', 'E633BDB9D73099C15DD80D03343C793E', '');
INSERT INTO `ibaremetal`.`t_baremetal_node`(`id`, `domain_id`, `center_id`, `node_mor`, `name`, `bmc_ip`, `bmc_user_password`, `ipmi_terminal_port`, `power_state`, `provision_state`, `console_enabled`, `deploy_info`, `properties`, `vcpus`, `memory`, `root_disk`, `maintenance`, `maintenance_reason`, `instance_id`, `instance_mor`, `driver`, `network_interface`, `console_interface`, `create_time`, `update_time`, `last_error`, `cpu_arch`, `capabilities`, `smart_card_enable`, `smart_card_ip`, `smart_card_username`, `smart_card_password`, `arch`, `vendor`, `source`, `resource_class`, `gpu_detail`) VALUES ('cc8c4ff5-d3d2-45c5-8b71-6d1c4344ad36', 'domainid', '8ef85ea5-3a97-4fc5-a2e3-75c0c2fcd226', '508a779e-0d1b-4bdf-97ee-a2ac093ebdc9', 'bm-x86_64-166', '*************', '{\"ipmi_username\":\"admin\",\"ipmi_password\":\"s0:t|Dgoe64xgOWMTBSy+nOo4vA==;30eec419a55401598379da157367cb87\"}', 7001, 'power on', 'active', 0, '{\"deploy_ramdisk\":\"ab52083e-e1c7-429e-b39e-097fa01388b3\",\"deploy_kernel\":\"f9d19289-c0de-48dc-89e2-ae6d226a0d3a\"}', '{\"cpu_detail\":\"2 * (Intel(R) Corporation Intel(R) Xeon(R) Gold 5218 CPU @ 2.30GHz) , 64 cores\",\"local_gb\":\"2234\",\"cpus\":\"64\",\"memory_detail\":\"8*32 GiB DDR4 , 256 GiB\",\"fru\":{\"Product_Name\":\"NF5280M5\",\"Product_Manufacturer\":\"Inspur\",\"Product_Serial\":\"220889790\"},\"disk_detail\":\"2.18 TiB (2*1117 GiB HDD SAS RAID0) + 10.92 TiB (5*3726 GiB HDD SATA RAID6)\",\"memory_mb\":\"262144\",\"tenant_ipmi_user\":{\"name\":\"clouduser\",\"key\":\"PDWm.kU9\"}}', '64', '262144', '2234', 1, NULL, '8a6f6ec98f1e3c3b018f2374609f0002', 'ca5d5484-b0bd-4557-af82-1296afde9030', 'ipmi', 'neutron', NULL, '2024-04-25 18:19:51', '2024-04-25 19:15:45', NULL, 'x86_64', 'iscsi_boot:True,boot_mode:bios,cpu_vt:true,cpu_aes:true,cpu_hugepages:true,cpu_hugepages_1g:true,cpu_txt:true', 0, NULL, NULL, '******', 'x86_64', 'Intel', 'REGISTER', 'E3665DDD9F3F02AD412E3C705758DF17', NULL);
INSERT INTO `ibaremetal`.`t_baremetal_node`(`id`, `domain_id`, `center_id`, `node_mor`, `name`, `bmc_ip`, `bmc_user_password`, `ipmi_terminal_port`, `power_state`, `provision_state`, `console_enabled`, `deploy_info`, `properties`, `vcpus`, `memory`, `root_disk`, `maintenance`, `maintenance_reason`, `instance_id`, `instance_mor`, `driver`, `network_interface`, `console_interface`, `create_time`, `update_time`, `last_error`, `cpu_arch`, `capabilities`, `smart_card_enable`, `smart_card_ip`, `smart_card_username`, `smart_card_password`, `arch`, `vendor`, `source`, `resource_class`, `gpu_detail`) VALUES ('fcdaf049-2e3a-4100-a156-3ebbacc3b4ce', 'domainid', '8ef85ea5-3a97-4fc5-a2e3-75c0c2fcd226', '23ccbbc5-165f-4250-b24d-fa9144b0017d', 'bm-kunpeng-63', '************', '{\"ipmi_username\":\"Administrator\",\"ipmi_password\":\"s0:t|NUA2SqOENlt3VN4zIYOhcg==;b2cdfe0f148c2508fb4dac22cd879a91\"}', 0, 'power on', 'active', 0, '{\"deploy_ramdisk\":\"8e89cf69-4767-42fe-afdf-5c5bce61118e\",\"deploy_kernel\":\"f0cd7d77-f17a-44e7-833e-936ac0e2ccfc\"}', '{\"cpu_detail\":\"2 * (HiSilicon HUAWEI Kunpeng 920 5220) , 64 cores\",\"local_gb\":\"1786\",\"cpus\":\"64\",\"memory_detail\":\"8*32 GiB DDR4 , 256 GiB\",\"fru\":{\"Product_Name\":\"Atlas 800 (Model 3000)\",\"Product_Manufacturer\":\"Huawei\",\"Product_Serial\":\"2102313LWMP0MB000265\"},\"disk_detail\":\"1.745 TiB (2*893.750 GiB SSD SATA RAID0)\",\"memory_mb\":\"262144\",\"tenant_ipmi_user\":{\"name\":\"clouduser\",\"key\":\"AX1vk)mV\"}}', '64', '262144', '1786', 0, NULL, '8a6f6ec98f1e3c3b018f1e9e836a0000', 'ef3ae9ef-3f74-4d1e-a927-d92fc6f598b9', 'ipmi', 'neutron', NULL, '2024-04-25 18:26:18', '2024-04-26 13:41:01', 'During sync_power_state, max retries exceeded for node 23ccbbc5-165f-4250-b24d-fa9144b0017d, node state None does not match expected state \'power on\'. Updating DB state to \'None\' Switching node to maintenance mode. Error: IPMI call failed: power status.', 'aarch64', 'iscsi_boot:True,boot_mode:uefi', 0, NULL, NULL, '******', 'aarch64', 'KunPeng', 'REGISTER', '9F819FBAAEE226E5B276C0B08B1823EF', NULL);











INSERT INTO `ibaremetal`.`t_baremetal_instance`(`id`, `domain_id`, `center_id`, `instance_mor`, `name`, `vdc_id`, `project_id`, `cluster_id`, `user_id`, `image_id`, `flavor_id`, `network_id`, `host`, `node_id`, `node_mor`, `status`, `power_state`, `create_time`, `update_time`, `last_error_message_detail`, `last_error_message`, `create_type`, `source_id`, `arch`, `vendor`, `agent_state`, `agent_install_start_time`, `agent_install_fault`, `agent_ip`, `agent_user_name`, `agent_access_key`, `agent_system_type`, `start_time`, `expire_time`, `charge_status`, `charge_model`, `charge_period_num`) VALUES ('8a6c3e1d8f1e3e9a018f1ee15afa0001', 'domainid', '8ef85ea5-3a97-4fc5-a2e3-75c0c2fcd226', '3b2fb314-20e9-48dd-9006-1f05975bab43', 'bm-ft', '2', '76673e5f-4172-4ea3-816f-02289a456e88', NULL, '8a87a1f83c93373b013c9347b37b0000', '4876df66-b5c9-4ffb-a72c-d49d8c3a18ab', '9b1c1a4f-5789-4f72-8fe4-6855a48abb68', NULL, NULL, '5cabe75b-72a6-4d1c-af3e-2fb081eb4d58', '7fc315a9-bffb-4b76-990c-d28065bdb44c', 'active', 'power on', '2024-04-27 17:27:16', '2024-04-27 17:27:16', NULL, NULL, 'IMAGE', '4876df66-b5c9-4ffb-a72c-d49d8c3a18ab', 'aarch64', 'FeiTeng', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ibaremetal`.`t_baremetal_instance`(`id`, `domain_id`, `center_id`, `instance_mor`, `name`, `vdc_id`, `project_id`, `cluster_id`, `user_id`, `image_id`, `flavor_id`, `network_id`, `host`, `node_id`, `node_mor`, `status`, `power_state`, `create_time`, `update_time`, `last_error_message_detail`, `last_error_message`, `create_type`, `source_id`, `arch`, `vendor`, `agent_state`, `agent_install_start_time`, `agent_install_fault`, `agent_ip`, `agent_user_name`, `agent_access_key`, `agent_system_type`, `start_time`, `expire_time`, `charge_status`, `charge_model`, `charge_period_num`) VALUES ('8a6e92768f12e8db018f152757670006', 'domainid', '8ef85ea5-3a97-4fc5-a2e3-75c0c2fcd226', 'c250aaa1-55c1-4066-a7e6-5e697e77797d', 'bm-haiguang', '2', '76673e5f-4172-4ea3-816f-02289a456e88', NULL, '8a87a1f83c93373b013c9347b37b0000', 'c1fe2d46-0e12-434b-a5a8-a1fe1f937992', 'dcf0d77c-2d34-473d-bd42-68a335a60a3a', NULL, NULL, '002d530f-fef0-42ab-8762-8a48bd21e19d', 'dc1dc4f5-4038-4590-939b-dc7bcc518f60', 'active', 'power on', '2024-04-25 20:07:30', '2024-04-25 20:07:30', NULL, NULL, 'IMAGE', 'c1fe2d46-0e12-434b-a5a8-a1fe1f937992', 'x86_64', 'Hygon', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ibaremetal`.`t_baremetal_instance`(`id`, `domain_id`, `center_id`, `instance_mor`, `name`, `vdc_id`, `project_id`, `cluster_id`, `user_id`, `image_id`, `flavor_id`, `network_id`, `host`, `node_id`, `node_mor`, `status`, `power_state`, `create_time`, `update_time`, `last_error_message_detail`, `last_error_message`, `create_type`, `source_id`, `arch`, `vendor`, `agent_state`, `agent_install_start_time`, `agent_install_fault`, `agent_ip`, `agent_user_name`, `agent_access_key`, `agent_system_type`, `start_time`, `expire_time`, `charge_status`, `charge_model`, `charge_period_num`) VALUES ('8a6f6ec98f1e3c3b018f1e9e836a0000', 'domainid', '8ef85ea5-3a97-4fc5-a2e3-75c0c2fcd226', 'ef3ae9ef-3f74-4d1e-a927-d92fc6f598b9', 'bm-kunpeng', '2', '76673e5f-4172-4ea3-816f-02289a456e88', NULL, '8a87a1f83c93373b013c9347b37b0000', '98c3e944-69ac-4d90-a547-9c92b09c61ae', 'f37e32ac-5430-4752-b394-12c2743b4963', NULL, NULL, 'fcdaf049-2e3a-4100-a156-3ebbacc3b4ce', '23ccbbc5-165f-4250-b24d-fa9144b0017d', 'active', 'power on', '2024-04-27 16:14:15', '2024-04-27 16:14:15', NULL, NULL, 'IMAGE', '98c3e944-69ac-4d90-a547-9c92b09c61ae', 'aarch64', 'KunPeng', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

