f = open("a.txt","w")
f.write("""INSERT INTO `ibaremetal`.`t_baremetal_node`(`id`, `domain_id`, `center_id`, `node_mor`, `name`, `bmc_ip`, `bmc_user_password`, `ipmi_terminal_port`, `power_state`, `provision_state`, `console_enabled`, `deploy_info`, `properties`, `vcpus`, `memory`, `root_disk`, `maintenance`, `maintenance_reason`, `instance_id`, `instance_mor`, `driver`, `network_interface`, `console_interface`, `create_time`, `update_time`, `last_error`, `cpu_arch`, `capabilities`, `smart_card_enable`, `smart_card_ip`, `smart_card_username`, `smart_card_password`, `arch`, `vendor`, `source`, `resource_class`, `gpu_detail`) VALUES
""")
for i in range(100):
    n = i+4
    s = """('cc8c4ff5-d3d2-45c5-8b71-6d1c4344%d', 'domainid', '8ef85ea5-3a97-4fc5-a2e3-75c0c2fcd226', '508a779e-0d1b-4bdf-97ee-a2ac093ebdc9', 'bm-%03d', '100.2.215.%d', '{\"ipmi_username\":\"admin\",\"ipmi_password\":\"s0:t|Dgoe64xgOWMTBSy+nOo4vA==;30eec419a55401598379da157367cb87\"}', 7001, 'power on', 'available', 0, '{\"deploy_ramdisk\":\"ab52083e-e1c7-429e-b39e-097fa01388b3\",\"deploy_kernel\":\"f9d19289-c0de-48dc-89e2-ae6d226a0d3a\"}', '{\"cpu_detail\":\"2 * (Intel(R) Corporation Intel(R) Xeon(R) Gold 5218 CPU @ 2.30GHz) , 64 cores\",\"local_gb\":\"2234\",\"cpus\":\"64\",\"memory_detail\":\"8*32 GiB DDR4 , 256 GiB\",\"fru\":{\"Product_Name\":\"NF5280M5\",\"Product_Manufacturer\":\"Inspur\",\"Product_Serial\":\"220889790\"},\"disk_detail\":\"2.18 TiB (2*1117 GiB HDD SAS RAID0) + 10.92 TiB (5*3726 GiB HDD SATA RAID6)\",\"memory_mb\":\"262144\",\"tenant_ipmi_user\":{\"name\":\"clouduser\",\"key\":\"PDWm.kU9\"}}', '64', '262144', '2234', 1, NULL, '8a6f6ec98f1e3c3b018f2374609f0002', 'ca5d5484-b0bd-4557-af82-1296afde9030', 'ipmi', 'neutron', NULL, '2024-03-20 18:19:51', '2024-03-20 19:15:45', NULL, 'x86_64', 'iscsi_boot:True,boot_mode:bios,cpu_vt:true,cpu_aes:true,cpu_hugepages:true,cpu_hugepages_1g:true,cpu_txt:true', 0, NULL, NULL, '******', 'x86_64', 'Intel', 'REGISTER', 'E3665DDD9F3F02AD412E3C705758DF17', NULL),\n"""%(n,n,n)
    f.write(s)
f.close()
f = open("b.txt","w")
f.write("""INSERT INTO `ibaremetal`.`t_baremetal_instance`(`id`, `domain_id`, `center_id`, `instance_mor`, `name`, `vdc_id`, `project_id`, `cluster_id`, `user_id`, `image_id`, `flavor_id`, `network_id`, `host`, `node_id`, `node_mor`, `status`, `power_state`, `create_time`, `update_time`, `last_error_message_detail`, `last_error_message`, `create_type`, `source_id`, `arch`, `vendor`, `agent_state`, `agent_install_start_time`, `agent_install_fault`, `agent_ip`, `agent_user_name`, `agent_access_key`, `agent_system_type`, `start_time`, `expire_time`, `charge_status`, `charge_model`, `charge_period_num`) VALUES
""")
for i in range(100):
    n = i+4
    s = """('8a6e92768f12e8db018f152757670%d', 'domainid', '8ef85ea5-3a97-4fc5-a2e3-75c0c2fcd226', 'c250aaa1-55c1-4066-a7e6-5e697e77797d', 'bm-%03d', '2', '76673e5f-4172-4ea3-816f-02289a456e88', NULL, '8a87a1f83c93373b013c9347b37b0000', 'c1fe2d46-0e12-434b-a5a8-a1fe1f937992', '4ba2c745-6a78-48f5-84a2-81cd98ab15f1', 'ddadd21b-b8a8-485e-b1e0-50183e069a65', NULL, 'cc8c4ff5-d3d2-45c5-8b71-6d1c4344%d', 'dc1dc4f5-4038-4590-939b-dc7bcc518f60', 'active', 'power on', '2024-03-20 20:07:30', '2024-03-20 20:07:30', NULL, NULL, 'IMAGE', 'c1fe2d46-0e12-434b-a5a8-a1fe1f937992', 'x86_64', 'Hygon', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),\n"""%(n,n,n)
    f.write(s)
f.close()

f = open("c.txt","w")
f.write("""INSERT INTO `ibaremetal`.`t_baremetal_instance_nic`(`id`, `port_mor`, `port_id`, `instance_id`, `floatip_id`, `ip`, `mac`, `network_id`, `network_mor`, `create_time`, `update_time`, `qos_policy_id`, `type`, `vif_type`) VALUES 
""")
for i in range(100):
    n = i+4
    s = """('xxxx-44f5-8e07-c9c5e2350%d', '81b9b949-b6c2-4e95-889c-a56801e182da', '81b9b949-b6c2-4e95-889c-a56801e182da', '8a6e92768f12e8db018f152757670%d', NULL, '192.168.10.%d', '00:1b:21:c4:67:90', 'ddadd21b-b8a8-485e-b1e0-50183e069a65', 'e5189443-7781-4fdb-899c-3cda6dd67406', '2024-04-27 16:14:15', '2024-04-27 16:41:24', NULL, NULL, 'ovs-bm'),\n"""%(n,n,100-n)
    f.write(s)
f.close()

INSERT INTO `itask`.`t_ba_task`(`task_id`, `center_id`, `domain_id`, `vdc_id`, `service_name`, `region`, `status`, `process`, `op_type`, `step_op_type`, `next_step_op_type`, `create_time`, `begin_time`, `end_time`, `exception_detail_zh`, `exception_detail_en`, `error_code`, `description`, `resource_id`, `exec_user`, `task_name`, `roletype`, `class_object`, `method`, `params`, `timeout`, `init_execute`, `params_json`, `step_status`) VALUES 
('8f5a5c6a-009c-4072-a2d3-84296afb751', NULL, 'domainid', '2', 'ibaremetal-service', 'regions', 'COMPLETE', 100, 'JOB_IPHYMACHINE_CREATE_INSTANCE', NULL, NULL, '2024-03-20 20:07:40', '2024-03-20 20:07:45', '2024-03-20 20:42:32', NULL, 'task normal execute finished', NULL, 'create instance by asynJob', '8a6e92768f12e8db018f1527576704', '8a87a1f83c93373b013c9347b37b0000', 'bm-004', 'superadmin', 'baremetalTaskService', 'doCreateInstance', NULL, 0, b'1', NULL, 'COMPLETE');

