#! /usr/bin/python3.6

# -*- coding: gbk -*-

import os
import subprocess
import tempfile
import re

# Kubernetes ������׺
DOMAIN_SUFFIX = ".svc.cluster.local"
ENABLE = ['i2fa-manager','iam-ha-headless','iam-ha-http','redis-ha-headless','redis-ha','iapps','ibase','icatalog','icharge','icluster','icompute','iconsole-proxy','idevops','iearth','iedgegrid','ilog','imesh','incloudhub','incloudmanager-api-gateway-regions','mariadb','incloudmanager-config','inetwork','irecovery','iregion','ireport','iresource-common','istellar','istorage','itask','iunifycloud','iworkflow']

# ����Ƿ��� sudo Ȩ��
if os.geteuid() != 0:
    print("Please run this script with sudo or as root.")
    exit(1)

# ���� /etc/hosts �ļ�
hosts_file = "/etc/hosts"
backup_file = "/etc/hosts.bak"
print(f"Backing up {hosts_file} to {backup_file}")
subprocess.run(["cp", hosts_file, backup_file])

# ��ȡ���������ռ��µ����з�������������ơ������ռ�� ClusterIP
print("Fetching services from Kubernetes...")
try:
    result = subprocess.run(
#        ["kubectl", "get", "svc", "--all-namespaces", "-o", "jsonpath={range .items[*]}{.metadata.name} {.metadata.namespace} {.spec.clusterIP}{'\n'}{end}"],
        ["kubectl", "get", "svc", "--all-namespaces"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        check=True
    )
except subprocess.CalledProcessError as e:
    print("Error fetching services from Kubernetes:", e.stderr)
    exit(1)

services = result.stdout.decode('utf-8').strip().split('\n')

# ʹ����ʱ�ļ��洢�µ� hosts ����
temp_hosts = tempfile.NamedTemporaryFile(delete=False)

# ���� Kubernetes �������������д����ʱ�ļ�
with open(temp_hosts.name, 'w') as temp_file:
    for service in services[1:]:
        # svc_name, namespace, cluster_ip = service.split()
        namespace,svc_name,type,cluster_ip,external_ip,port,age = service.split()
        # ����û�� ClusterIP �ķ��� (headless ����)
        if cluster_ip == "None":
            continue
        if svc_name not in ENABLE:
            continue
        # ���������� Kubernetes ��������
        svc_domain = f"{svc_name}.{namespace}{DOMAIN_SUFFIX}"
        svc_domain = f"{svc_name}"
        temp_file.write(f"{cluster_ip} {svc_domain}\n")
        if "redis-ha" == svc_name:
            temp_file.write(f"{cluster_ip} redis-ha-headless\n")

# ��ȡ���е� /etc/hosts �ļ�����
with open(hosts_file, 'r') as hosts:
    hosts_lines = hosts.readlines()

# ɾ���ɵ� .svc.cluster.local ����ӳ��
#new_hosts_lines = [line for line in hosts_lines if not re.search(r'\.svc\.cluster\.local', line)]
new_hosts_lines = []
for line in hosts_lines:
    if 'Kubernetes services domain mappings'in line:
        break
    new_hosts_lines.append(line)
# ���µ�����ӳ��׷�ӵ� /etc/hosts �ļ�ĩβ
with open(hosts_file, 'w') as hosts:
    hosts.writelines(new_hosts_lines)
    with open(temp_hosts.name, 'r') as temp_file:
        hosts.write("\n# Kubernetes services domain mappings\n")
        hosts.write(temp_file.read())

# ������ʱ�ļ�
os.unlink(temp_hosts.name)

print(f"Updated {hosts_file} with Kubernetes service domains.")
